import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getUsers, createUser, updateUser, deleteUser } from '@/services/api'
import type { User, UserFormData } from '@/types/user'

// Query keys for cache management
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...userKeys.lists(), { filters }] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
}

export const useUsers = () => {
  return useQuery<User[]>({
    queryKey: userKeys.lists(),
    queryFn: getUsers,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export const useCreateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
    },
    onError: (error) => {
      console.error('Error creating user:', error)
    },
  })
}

export const useUpdateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ userId, userData }: { userId: string; userData: Partial<User> }) =>
      updateUser(userId, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
    },
    onError: (error) => {
      console.error('Error updating user:', error)
    },
  })
}

export const useDeleteUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
    },
    onError: (error) => {
      console.error('Error deleting user:', error)
    },
  })
}

export const useUserManagement = () => {
  const { data: users = [], isLoading, error, refetch } = useUsers()
  const createUserMutation = useCreateUser()
  const updateUserMutation = useUpdateUser()
  const deleteUserMutation = useDeleteUser()

  const addUser = async (userData: UserFormData) => {
    await createUserMutation.mutateAsync(userData)
  }

  const updateUserData = async (userId: string, updatedData: Partial<User>) => {
    await updateUserMutation.mutateAsync({ userId, userData: updatedData })
  }

  const removeUser = async (userId: string) => {
    await deleteUserMutation.mutateAsync(userId)
  }

  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    const newStatus = !currentStatus
    await updateUserData(userId, { isActive: newStatus })
  }

  const resetUserPassword = async (userId: string) => {
    // In a real application, this would make an API call to reset the password
    console.log(`Password reset requested for user ${userId}`)
    // You could show a toast notification here
  }

  const filterUsers = (searchTerm: string, roleFilter: string, statusFilter: string) => {
    return users.filter((user: User) => {
      const fullName = `${user.firstName} ${user.lastName}`
      const matchesSearch = searchTerm === '' || 
        fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.employeeId?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesRole = roleFilter === 'All Roles' || 
        roleFilter.toLowerCase() === user.role?.toLowerCase()
      
      const matchesStatus = statusFilter === 'All Status' || 
        (statusFilter === 'ACTIVE' && user.isActive) ||
        (statusFilter === 'INACTIVE' && !user.isActive)
      
      return matchesSearch && matchesRole && matchesStatus
    })
  }

  return {
    users,
    isLoading,
    error,
    refetch,
    addUser,
    updateUser: updateUserData,
    deleteUser: removeUser,
    toggleUserStatus,
    resetUserPassword,
    filterUsers,
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
  }
}
