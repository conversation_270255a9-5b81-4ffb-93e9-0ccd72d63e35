/**
 * Utility functions for handling user permissions
 */

/**
 * Get all user permissions from localStorage
 * @returns string[] Array of permission strings
 */
export const getUserPermissions = (): string[] => {
  const permissionsStr = localStorage.getItem('permissions');
  if (!permissionsStr) return [];
  
  try {
    const permissions = JSON.parse(permissionsStr);
    return Array.isArray(permissions) ? permissions : [];
  } catch (error) {
    console.error('Error parsing permissions from localStorage:', error);
    return [];
  }
};

/**
 * Check if user has a specific permission
 * @param permission Permission to check
 * @returns boolean True if user has the permission
 */
export const hasPermission = (permission: string): boolean => {
  const permissions = getUserPermissions();
  return permissions.includes(permission);
};

/**
 * Check if user has at least one of the given permissions
 * @param requiredPermissions Array of permissions to check
 * @returns boolean True if user has at least one of the required permissions
 */
export const hasAnyPermission = (requiredPermissions: string[]): boolean => {
  const userPermissions = getUserPermissions();
  return requiredPermissions.some(permission => userPermissions.includes(permission));
};

/**
 * Check if user has all of the given permissions
 * @param requiredPermissions Array of permissions to check
 * @returns boolean True if user has all required permissions
 */
export const hasAllPermissions = (requiredPermissions: string[]): boolean => {
  const userPermissions = getUserPermissions();
  return requiredPermissions.every(permission => userPermissions.includes(permission));
};
