import React, { useState, useEffect } from 'react'
import {
    <PERSON><PERSON><PERSON>,
    CircularProgress,
    <PERSON>rid,
    Box,
    <PERSON>ert,
    TextField,
    Button
} from '@mui/material'
import Header from '../components/general/Header'
import { useAuth } from '../context/AuthContextProvider'
import { consumableService } from '../services/api'
import { getUserPermissions } from '../utils/permissionUtils'
import {
    WeeklySummary,
    ProjectCosts,
    ProductCategoryCosts
} from '../components/Dashboard'
import { ITransaction } from '../components/PickPage/DropTable'

// TypeScript interfaces for the API response
interface WeeklySummary {
    weekNumber: number
    startDate: string
    endDate: string
    totalCost: number
}

interface Resource {
    id: string
    name: string
    resourceCode: string
    unitType: string
    unitSize: number
    imageUrl: string
    resourceCategory: string
    minInventoryQty: number
    maxInventoryQty: number
    resourceType: string
    deletedAt: string | null
}

interface ResourceCost {
    resource: Resource
    cost: number
}

interface Project {
    id: string
    projectCode: string
    projectName: string
    startDate: string
    dueDate: string
    createdAt: string
    updatedAt: string
}

interface ProjectCost {
    project: Project
    totalCost: number
    resourcesCost: ResourceCost[]
}

interface ResourceCategoryCost {
    categoryName: string
    totalCost: number
}

interface DashboardData {
    weeklySummaries: WeeklySummary[]
    projectCosts: ProjectCost[]
    resourceCategoryCosts: ResourceCategoryCost[]
}

const Dashboard = () => {
    const [data, setData] = useState<DashboardData | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Set default dates: today and 2 months ago
    const getTodayFormatted = () => {
        const today = new Date()
        return today.toISOString().split('T')[0]
    }

    const getStartDateFormatted = () => {
        const today = new Date()
        const twoMonthsAgo = new Date(today.setMonth(today.getMonth() - 2))
        return twoMonthsAgo.toISOString().split('T')[0]
    }

    const [startDate, setStartDate] = useState<string>(getStartDateFormatted())
    const [endDate, setEndDate] = useState<string>(getTodayFormatted())
    const [dateError, setDateError] = useState<string | null>(null)
    const { token } = useAuth()
    const [recentActivities, setRecentActivities] = useState<ITransaction[]>([]);    // Get permissions using utility function
    const permissions = getUserPermissions();
    const hasReportsPermission = permissions.includes('reports');

    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                setLoading(true)
                const response = await consumableService.get('/reports/summary', token!)
                setData(response.data)
                setError(null)
            } catch (err) {
                console.error('Error fetching dashboard data:', err)
                setError('Failed to load dashboard data. Please try again later.')
            } finally {
                setLoading(false)
            }
        }

        const fetchRecentActivities = async () => {
            try {
                const response = await consumableService.get("/resources/inventory/transactions?limit=10&includeZeroQuantity=true", token);
                if (response.status === 200) {
                    setRecentActivities(response.data.transactions);
                    // ...existing code...
                }
            } catch (error) {
                console.error("Error fetching recent activities:", error);
            }
        }

        if (token) {
            // Always fetch recent activities
            fetchRecentActivities()
            
            // Only fetch dashboard data if user has reports permission
            if (hasReportsPermission) {
                fetchDashboardData()
            } else {
                setLoading(false)
            }
        }
    }, [token, hasReportsPermission])

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount)
    }

    const formatDate = (dateString: string) => {        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        })
    }

    const handleDateFilter = async () => {
        // Only allow filtering if user has reports permission
        if (!hasReportsPermission) return;

        // Validate dates
        const start = new Date(startDate)
        const end = new Date(endDate)
        const daysDifference = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))

        if (end <= start) {
            setDateError('End date must be greater than start date')
            return
        }

        if (daysDifference < 8) {
            setDateError('Date range must be at least 8 days apart')
            return
        }

        setDateError(null)

        try {
            setLoading(true)
            const response = await consumableService.get(
                `/reports/summary?startDate=${startDate}&endDate=${endDate}`,
                token!
            )
            setData(response.data)
        } catch (err) {
            setError('Failed to filter data')
        } finally {
            setLoading(false)
        }    }

    const clearDateFilter = async () => {
        // Only allow clearing filter if user has reports permission
        if (!hasReportsPermission) return;

        // Reset to default dates (2 months ago to today)
        setStartDate(getStartDateFormatted())
        setEndDate(getTodayFormatted())
        setDateError(null)

        // Refetch all data without date filters
        try {
            setLoading(true)
            const response = await consumableService.get('/reports/summary', token!)
            setData(response.data)
            setError(null)
        } catch (err) {
            setError('Failed to load data')
        } finally {
            setLoading(false)
        }
    }

    if (loading) {
        return (
            <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
                <Header />
                <Box className="flex-1 flex justify-center items-center">
                    <CircularProgress size={60} />
                </Box>
            </div>
        )
    }

    if (error) {
        return (
            <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
                <Header />
                <Box className="flex-1 flex justify-center items-center p-6">
                    <Alert severity="error" className="max-w-md">
                        {error}
                    </Alert>
                </Box>
            </div>
        )
    } return (
        <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
            <Header />
            <Box className="flex-1 overflow-y-auto p-3">
                <div className="w-full">                    <div className="mb-2 flex justify-between">
                        <div>
                            <Typography variant="h5" className="font-bold text-gray-800">
                                Dashboard Overview
                            </Typography>
                            <Typography variant="body2" className="text-gray-600">
                                {hasReportsPermission 
                                    ? "Track your project costs and weekly summaries"
                                    : "View recent activities"
                                }
                            </Typography>
                        </div>
                        {hasReportsPermission && (
                            <div>
                                <div className="flex flex-wrap items-center gap-2">
                                    <TextField
                                        label="Start Date"
                                        type="date"
                                        value={startDate}
                                        onChange={(e) => {
                                            setStartDate(e.target.value)
                                            setDateError(null)
                                        }}
                                        size="small"
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                        sx={{ minWidth: 140 }}
                                        error={!!dateError}
                                    />
                                    <TextField
                                        label="End Date"
                                        type="date"
                                        value={endDate}
                                        onChange={(e) => {
                                            setEndDate(e.target.value)
                                            setDateError(null)
                                        }}
                                        size="small"
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                        sx={{ minWidth: 140 }}
                                        error={!!dateError}
                                    />
                                    {dateError && (
                                        <Typography variant="caption" color="error" sx={{ ml: 1 }}>
                                            {dateError}
                                        </Typography>
                                    )}
                                    <div className="flex gap-2">
                                        <Button
                                            variant="contained"
                                            size="small"
                                            onClick={handleDateFilter}
                                            disabled={!startDate || !endDate}
                                            sx={{
                                                bgcolor: 'blue.500',
                                                '&:hover': { bgcolor: 'blue.600' },
                                                minWidth: 70,
                                                flex: 1
                                            }}
                                        >
                                            Filter
                                        </Button>
                                        <Button
                                            variant="outlined"
                                            size="small"
                                            onClick={clearDateFilter}
                                            sx={{ minWidth: 60, flex: 1 }}
                                        >
                                            Clear
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>                    <Grid container spacing={1}>
                        {hasReportsPermission ? (
                            <>
                                {/* Weekly Summaries Section */}
                                <Grid item xs={12} lg={6}>
                                    <Box sx={{ height: 'calc(50vh)', overflow: 'auto' }}>
                                        <WeeklySummary
                                            data={data?.weeklySummaries || []}
                                            startDate={startDate}
                                            endDate={endDate}
                                            dateError={dateError}
                                            onStartDateChange={(value) => {
                                                setStartDate(value)
                                                setDateError(null)
                                            }}
                                            onEndDateChange={(value) => {
                                                setEndDate(value)
                                                setDateError(null)
                                            }}
                                            onFilter={handleDateFilter}
                                            onClear={clearDateFilter}
                                            formatCurrency={formatCurrency}
                                            formatDate={formatDate}
                                        />
                                    </Box>
                                </Grid>

                                {/* Recent Activities */}
                                <Grid item xs={12} lg={6}>
                                    <Box sx={{ height: 'calc(50vh)', overflow: 'auto', bgcolor: 'white', borderRadius: 2, p: 2, boxShadow: 1 }}>
                                        <Typography variant="h6" className="font-semibold text-gray-800 mb-3">
                                            Recent Activities
                                        </Typography>
                                        {recentActivities.length > 0 ? (
                                            <ul className="divide-y divide-gray-200 customScroll overflow-y-auto max-h-[calc(100%-2rem)]">
                                                {recentActivities.map((activity) => (
                                                    <li key={activity.id} className="py-3">
                                                        <div className="flex flex-col">
                                                            <div className="flex items-center mb-1">
                                                                <span className={`mr-2 px-2 py-1 text-xs rounded ${activity.actionType === 'pick' ? 'bg-red-100 text-red-800' :
                                                                    activity.actionType === 'add' ? 'bg-green-100 text-green-800' :
                                                                        'bg-blue-100 text-blue-800'
                                                                    }`}>
                                                                    {activity.actionType === 'add' ? "ADD" : activity.actionType === 'pick' ? "PICK" : activity.actionType.toUpperCase()}
                                                                </span>
                                                                <span className="text-sm text-gray-500 ml-auto">
                                                                    {new Date(activity.timestamp).toLocaleString()}
                                                                </span>
                                                            </div>

                                                            <div>
                                                                <span className="font-medium">{activity.userName}</span>
                                                                <span className="mx-1">{activity.actionType === 'pick' ? 'picked' :
                                                                    activity.actionType === 'add' ? 'added' : 'dropped'}</span>
                                                                <span className="font-medium">{activity.quantity}</span>
                                                                <span> </span>
                                                                <span className="font-medium">{activity.resourceName}</span>
                                                            </div>
                                                        </div>
                                                    </li>
                                                ))}
                                            </ul>
                                        ) : (
                                            <p className="text-gray-500">No recent activities</p>
                                        )}
                                    </Box>
                                </Grid>

                                {/* Top 5 Projects Section */}
                                <Grid item xs={12} lg={6} className="flex">
                                    <Box className="w-full" sx={{ height: 'calc(50vh)', overflow: 'auto' }}>
                                        <ProjectCosts
                                            data={data?.projectCosts || []}
                                            formatCurrency={formatCurrency}
                                        />
                                    </Box>
                                </Grid>

                                {/* Product Category Costs Section */}
                                <Grid item xs={12} lg={6} className="flex">
                                    <Box className="w-full" sx={{ height: 'calc(50vh)', overflow: 'auto' }}>
                                        <ProductCategoryCosts
                                            data={data?.resourceCategoryCosts || []}
                                            formatCurrency={formatCurrency}
                                        />
                                    </Box>
                                </Grid>
                            </>
                        ) : (
                            /* Show only Recent Activities for users without reports permission */
                            <Grid item xs={12}>
                                <Box sx={{ height: 'calc(90vh)', overflow: 'auto', bgcolor: 'white', borderRadius: 2, p: 3, boxShadow: 1 }}>
                                    <Typography variant="h6" className="font-semibold text-gray-800 mb-4">
                                        Recent Activities
                                    </Typography>
                                    {recentActivities.length > 0 ? (
                                        <ul className="divide-y divide-gray-200 customScroll overflow-y-auto max-h-[calc(100%-3rem)]">
                                            {recentActivities.map((activity) => (
                                                <li key={activity.id} className="py-4">
                                                    <div className="flex flex-col">
                                                        <div className="flex items-center mb-2">
                                                            <span className={`mr-3 px-3 py-1 text-sm rounded-full ${activity.actionType === 'pick' ? 'bg-red-100 text-red-800' :
                                                                activity.actionType === 'add' ? 'bg-green-100 text-green-800' :
                                                                    'bg-blue-100 text-blue-800'
                                                                }`}>
                                                                {activity.actionType === 'add' ? "ADD" : activity.actionType === 'pick' ? "PICK" : activity.actionType.toUpperCase()}
                                                            </span>
                                                            <span className="text-sm text-gray-500 ml-auto">
                                                                {new Date(activity.timestamp).toLocaleString()}
                                                            </span>
                                                        </div>

                                                        <div className="text-base">
                                                            <span className="font-medium">{activity.userName}</span>
                                                            <span className="mx-1">{activity.actionType === 'pick' ? 'picked' :
                                                                activity.actionType === 'add' ? 'added' : 'dropped'}</span>
                                                            <span className="font-medium">{activity.quantity}</span>
                                                            <span> </span>
                                                            <span className="font-medium">{activity.resourceName}</span>
                                                        </div>
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    ) : (
                                        <p className="text-gray-500 text-center py-8">No recent activities</p>
                                    )}
                                </Box>
                            </Grid>
                        )}
                    </Grid>
                </div>
            </Box>
        </div>
    )
}

export default Dashboard