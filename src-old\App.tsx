import "./App.css";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import Consumables from "./pages/invertory-management/ConsumablesPage";
import Consumable from "./pages/invertory-management/ConsumablePage";
import Pick from "./pages/pickdrop/Pick";
import Suppliers from "./pages/supplier-management/Suppliers";
import Login from "./pages/auth/Login";
import ResetPassword from "./pages/auth/ResetPassword";
import PasswordResetSuccess from "./pages/auth/PasswordResetSuccess";
import ProtectedRouteForAdmin from "./hooks/ProtectedRouteForAdmin";
import ProtectedRouteForWorker from "./hooks/ProtectedRouteForWorker";
import PickOrDrop from "./pages/pickdrop/PickOrDrop";
import { Toaster } from "react-hot-toast";
import Projects from "./pages/project-management/Projects";
import Users from "./pages/user-management/Users";
import Report from "./pages/Report";
import Dashboard from "./pages/Dashboard";
import Resources from "./pages/resource-management/Resources";
import AddResource from "./pages/resource-management/AddResource";

function App() {
  return (
    <>
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: "green",
            color: "white",
          },
          error: {
            style: {
              background: "red",
              color: "white",
            },
          },
        }}
      />{" "}
      <Router>
        <div className="App">
          {/* Make sure the Toaster is properly rendered */}
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route
              path="/password-reset-success"
              element={<PasswordResetSuccess />}
            />{" "}
            <Route
              path="/consumables/:method"
              element={
                <ProtectedRouteForWorker>
                  <Pick />
                </ProtectedRouteForWorker>
              }
            />
            <Route
              path="/"
              element={
                <ProtectedRouteForAdmin>
                  <Dashboard />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/resources"
              element={
                <ProtectedRouteForAdmin>
                  {/* <Resources /> */}
                  <Consumables />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/choose"
              element={
                <ProtectedRouteForWorker>
                  <PickOrDrop />
                </ProtectedRouteForWorker>
              }
            />
            {/* <Route path='/' element={
            <ProtectedRouteForAdmin>
              
            </ProtectedRouteForAdmin>
          } /> */}
            <Route
              path="/consumables/add"
              element={
                <ProtectedRouteForAdmin>
                  <Consumable />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/suppliers"
              element={
                <ProtectedRouteForAdmin>
                  <Suppliers />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/project-management"
              element={
                <ProtectedRouteForAdmin>
                  <Projects />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/user-management"
              element={
                <ProtectedRouteForAdmin>
                  <Users />
                </ProtectedRouteForAdmin>
              }
            />
            <Route
              path="/report"
              element={
                <ProtectedRouteForAdmin>
                  <Report />
                </ProtectedRouteForAdmin>
              }
            />
          </Routes>
        </div>
      </Router>
    </>
  );
}

export default App;
