import React from "react";
import ProductContextProvider from "./ProductContextProvider";
import AuthContextProvider from "./AuthContextProvider";

interface ContextProviderProps {
  children: React.ReactNode
}


const ContextProvider: React.FC<ContextProviderProps> = ({ children }) => {
  return (
    <AuthContextProvider>
      <ProductContextProvider>
        {children}
      </ProductContextProvider>
    </AuthContextProvider>
  )
};

export default ContextProvider;
