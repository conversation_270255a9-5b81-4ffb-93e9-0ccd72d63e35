"use client"

import { type ColumnDef } from "@tanstack/react-table"
import { type User } from "@/types/user"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Edit, RotateCcw, Trash2 } from "lucide-react"

const getRoleBadgeColor = (role: string) => {
  switch (role) {
    case "admin":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "supervisor":
      return "bg-red-100 text-red-800 border-red-200";
    case "mechanic":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

interface GetColumnsProps {
  toggleUserStatus: (userId: string, currentStatus: boolean) => void;
  resetUserPassword: (userId: string) => void;
  // Add other action handlers as needed, e.g., for edit and delete
}

export const getColumns = ({
  toggleUserStatus,
  resetUserPassword,
}: GetColumnsProps): ColumnDef<User>[] => [
  {
    accessorKey: "employeeId",
    header: "Employee ID",
  },
  {
    accessorKey: "firstName",
    header: "Employee Name",
    cell: ({ row }) => `${row.original.firstName} ${row.original.lastName}`,
  },
  {
    accessorKey: "role",
    header: "Roles",
    cell: ({ row }) => (
      <Badge
        className={`${getRoleBadgeColor(
          row.original.role
        )} rounded px-3 py-2 border-0 `}
        variant="outline"
      >
        {row.original.role.toUpperCase()}
      </Badge>
    ),
  },
  {
    accessorKey: "email",
    header: "Email Address",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center bg-grey-50 w-fit px-3 py-2 rounded">
          <Switch
            checked={user.isActive}
            onCheckedChange={() =>
              toggleUserStatus(user.id, user.isActive)
            }
            className={`mr-2 ${
              user.isActive ? "data-[state=checked]:bg-green-600" : ""
            }`}
          />
          <span className={`text-sm font-medium text-grey-900`}>
            {user.isActive ? "ACTIVE" : "INACTIVE"}
          </span>
        </div>
      )
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original
      // Placeholder functions for actions
      const handleEdit = () => console.log("Editing user:", user.id);
      const handleDelete = () => console.log("Deleting user:", user.id);

      return (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            className="bg-grey-50 h-11 w-10"
            onClick={handleEdit}
          >
            <Edit className="!h-5 !w-5" />
          </Button>
          <Button
            variant="ghost"
            className="bg-grey-50 h-11 w-10"
            onClick={() => resetUserPassword(user.id)}
          >
            <RotateCcw className="!h-5 !w-5" />
          </Button>
          <Button
            variant="ghost"
            className="bg-red-50 h-11 w-10"
            onClick={handleDelete}
          >
            <Trash2 className="!h-5 !w-5 text-red-400" />
          </Button>
        </div>
      )
    },
  },
]
