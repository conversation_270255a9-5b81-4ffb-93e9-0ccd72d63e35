export interface IFileUploader {
    selectedImage: string;
    shareFile: (data: File) => void;
    isEditable: boolean;
    onImageUpload?: (url: string) => void;
}

export interface CropState {
    x: number;
    y: number;
}

export interface CroppedArea {
    x: number;
    y: number;
    width: number;
    height: number;
}

export interface MediaSize {
    naturalWidth: number;
    naturalHeight: number;
}

export interface CropperViewProps {
    imageToCrop: string;
    crop: CropState;
    zoom: number;
    onCropChange: (crop: CropState) => void;
    onCropComplete: (croppedArea: any, croppedAreaPixels: CroppedArea) => void;
    onZoomChange: (zoom: number) => void;
    onMediaLoaded: (mediaSize: MediaSize) => void;
}

export interface ZoomSliderProps {
    zoom: number;
    onZoomChange: (zoom: number) => void;
}

export interface ImagePreviewProps {
    selectedImage: string;
}
