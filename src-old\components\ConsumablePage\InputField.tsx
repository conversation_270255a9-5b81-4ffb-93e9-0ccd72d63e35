import React, { useState } from "react";

interface InputFieldProps {
    label: string;
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
    type?: string;
    error?: string[];
    showPasswordToggle?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
    label, value, onChange, disabled = false, type = "text", error, showPasswordToggle = false
}) => {
    const [showPassword, setShowPassword] = useState(false);
    
    const isPasswordType = type === "password";
    const inputType = isPasswordType && showPassword ? "text" : type;    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    // ...existing code...
    
    return (
        <div className='flex flex-col '>
            <div className="flex flex-col gap-2">
                <label className="" >{label} <span className="text-red-500">*</span></label>
                <div className="relative">
                    <input
                        type={inputType}
                        value={value}
                        className='rounded-md border px-2 h-11 outline-pickTBlue text-sm font-medium w-full'
                        onChange={(e) => onChange(e.target.value)}
                        disabled={disabled}
                    />
                    {isPasswordType && showPasswordToggle && (
                        <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={togglePasswordVisibility}
                        >
                            {showPassword ? (
                                // Eye slash icon (hide password)
                                <svg
                                    className="h-5 w-5 text-gray-400 hover:text-gray-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L8.464 8.464"
                                    />
                                </svg>
                            ) : (
                                // Eye icon (show password)
                                <svg
                                    className="h-5 w-5 text-gray-400 hover:text-gray-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                </svg>
                            )}
                        </button>
                    )}
                </div>
            </div>
            <div className={`${error ? "block" : "opacity-0"}`}>
                {
                    error?.map(err => {
                        if (err.length > 0) {
                            return <label className={`text-xs text-red-500 `}>{err}</label>
                        }
                    })
                }
            </div>
        </div>
    );
};

export default InputField;