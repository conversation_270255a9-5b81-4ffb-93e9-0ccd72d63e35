import { useNavigate } from 'react-router-dom';
import { IConsumable } from '../../models/Consumable';
import { EditIcon, PrinterIcon } from 'lucide-react';

interface IConsumableInventory {
    "id": string,
    "name": string,
    "resourceCode": string,
    "unitType": string,
    "unitSize": number,
    "imageUrl": string,
    "resourceCategory": string,
    "totalAvailableQuantity": number,
    "totalAddedQuantity": number,
    "averagePricePerUnit": number
}

interface IStockTabke {
    handleOpen: (data: IConsumable) => void
    datas: IConsumable[]
}

const StockTable = ({ handleOpen, datas }: IStockTabke) => {
    // ...existing code...
    const navigate = useNavigate()
    const handleAdd = (data: IConsumable) => {
        navigate(`/consumables/add?id=${data.id}`)
    }
    return (
        <div className='overflow-x-auto overflow-y-auto h-full customScroll'>
            <table className='w-full min-w-[640px]'>
                <thead className='sticky top-0 bg-white border-black font-manrope text-xs font-bold'>
                    <tr className='bg-[#F2F4F7]'>
                        <th className='border text-start p-2 w-1/12'>CODE</th>
                        <th className='border text-start p-2 w-1/12'>IMAGE</th>
                        <th className='border text-start p-2 w-2/12'>ITEM NAME</th>
                        <th className='border text-start p-2 w-1/12'>UNIT</th>
                        <th className='border text-start p-2 w-1/12'>QTY</th>
                        <th className='border text-start p-2 w-1/12'>Action</th>
                    </tr>
                </thead>
                <tbody className=''>
                    {datas.map((data, index) => {
                        // Determine row background color based on inventory levels
                        let bgColor = '';
                        if (data?.totalAvailableQuantity > data?.maxInventoryQty) {
                            bgColor = 'bg-blue-100';
                        } else if (data?.totalAvailableQuantity < data?.minInventoryQty) {
                            bgColor = 'bg-red-100';
                        }
                        // When between min and max, bgColor remains empty (default)
                        
                        return (
                        <tr key={index} className={`font-nunito text-sm font-normal ${bgColor}`}>
                            <td className="p-2">
                                {data?.resourceCode}
                            </td>
                            <td className="p-2">
                                <img src={data?.imageUrl} alt="Img" className='w-14 h-14 object-center' />
                            </td>
                            <td className="p-2">
                                <div className='flex gap-3 items-center'>
                                    <h3>{data?.name}</h3>
                                </div>
                            </td>
                            <td className="p-2">{data?.unitType}</td>
                            <td className="p-2">{data?.unitType === "pack" ? `${data?.totalAvailableQuantity} EA` : data?.totalAvailableQuantity}</td>
                            <td className="p-2">
                                <div className='flex gap-3 flex-wrap'>
                                  
                                    <div className='bg-gray-500 rounded-lg p-2' onClick={() => handleAdd(data)}>
                                        <EditIcon className='text-white' />
                                    </div>
                                </div>
                            </td>
                        </tr>
                        );
                    })}
                </tbody>
            </table>
        </div>
    )
}

export default StockTable

