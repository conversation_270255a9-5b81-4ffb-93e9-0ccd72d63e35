import React from 'react'

interface ILabeledInput {
    label: string,
    palceholder: string
    type?: string
    handleChange: (key: string, value: string) => void,
    name: string,
    value: string | number
}

const LabeledInput = ({ label, palceholder, type, handleChange, name, value }: ILabeledInput) => {
    return (
        <div className='flex flex-col gap-2'>
            <label htmlFor="" className='text-sm font-medium'>{label}</label>
            {
                type === "phoneNumber" ?
                    <div className='border rounded-md p-2 flex gap-2 bg-white'>
                        <input type="number" className='bg-transparent w-full outline-none text-sm font-medium' placeholder={palceholder} onChange={(e) => handleChange(name, e.target.value)} value={value} />
                    </div> :
                    <input type="text" className='border rounded-md p-2 text-sm font-medium' placeholder={palceholder} onChange={(e) => handleChange(name, e.target.value)} value={value} />
            }
        </div>
    )
}

export default LabeledInput