import { useEffect, useState } from "react";
import Header from "../../components/general/Header";
import GProductInfo from "../../components/ConsumablePage/GProductInfo";
import { Link, useSearchParams } from "react-router-dom";
import GReceiptInfo from "../../components/ConsumablePage/GReceiptInfo";
import AddSupplierModal from "../../components/ConsumablePage/AddSupplierModal";
import { useAuth } from "../../context/AuthContextProvider";
import { consumableService } from "../../services/api";
import InventoryList from "../../components/ConsumablePage/InventoryList";

interface ISupplier {
  id: string;
  name: string;
}

interface IResource {
  id: string;
  resourceCode: string;
  productName: string;
  category: string;
  unitType: number;
  quantity: number;
  imageUrl: string;
  resourceId?: string;
}

const Consumable = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");
  const [consumableData, setConsumableData] = useState<IResource>();

  const { user, token } = useAuth();

  const [supplierData, setSupplierData] = useState({
    supplierResourceCode: "",
    quantity: "",
    unitPrice: "",
    addedOn: getFormattedDate(),
    location: "",
  });
  const [supplier, setSupplier] = useState<ISupplier | undefined>();
  const [inventory, setInventory] = useState();
  const [supplierModalStatus, setSupplierModalStatus] = useState(false);
  const [trigger, setTrigger] = useState(0);
  const [message, setMessage] = useState<
    { message: string; type: string } | undefined
  >({
    message: "",
    type: "",
  });
  const [error, setError] = useState("");

  function getFormattedDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  }

  const handleSupplierData = (key: string, vale: string) => {
    setSupplierData((prev) => {
      return {
        ...prev,
        [key]: vale,
      };
    });
  };

  const handleSupplierModalStatus = (status: boolean) => {
    // ...existing code...
    setSupplierModalStatus(status);
  };

  const fetchInventory = async (id: string) => {
    try {
      const inventory = await consumableService.get(
        `resources/${id}/inventory`,
        token
      );
      // ...existing code...
      if (inventory.status === 200) {
        setInventory(inventory.data);
      }
    } catch (error) {
      console.error("Error fetching inventory:", error);
      handleMessage("Failed to fetch inventory", "error");
    }
  };

  const handleMessage = (msg: string, type: string) => {
    setMessage({
      message: msg,
      type,
    });
    setTimeout(() => {
      setMessage(undefined);
    }, 3000);
  };

  const handleAddItem = async () => {
    const inventory = {
      resourceId: consumableData?.id,
      supplierId: supplier?.id,
      userId: user?.sub,
      quantity: parseInt(supplierData?.quantity),
      pricePerUnit: parseFloat(supplierData?.unitPrice),
      supplierResourceCode: supplierData.supplierResourceCode,
      addedOn: supplierData.addedOn,
      // unitType: consumableData?.unitType,
      unitType: "individual",
      location: supplierData?.location,
    };
    function validateInventory(inventory: any) {
      const errors = [];

      if (!inventory.resourceId || typeof inventory.resourceId !== "string") {
        errors.push("Consumable ID is required ");
      }

      if (!inventory.supplierId || typeof inventory.supplierId !== "string") {
        errors.push("Supplier ID is required ");
      }

      if (!inventory.userId || typeof inventory.userId !== "string") {
        errors.push("Staff ID is required ");
      }

      if (
        !inventory.supplierResourceCode ||
        typeof inventory.supplierResourceCode !== "string"
      ) {
        errors.push("Supplier Resource Code is required ");
      }

      if (!Number.isInteger(inventory.quantity) || inventory.quantity <= 0) {
        errors.push("Quantity must be a positive integer.");
      }

      if (
        typeof inventory.pricePerUnit !== "number" ||
        isNaN(inventory.pricePerUnit) ||
        inventory.pricePerUnit <= 0
      ) {
        errors.push("Unit price must be a positive number.");
      }

      if (!inventory.addedOn || isNaN(new Date(inventory.addedOn).getTime())) {
        errors.push("Please a date");
      }

      if (!inventory.location || typeof inventory.location !== "string") {
        errors.push("Location is required");
      }

      return errors.length > 0 ? { valid: false, errors } : { valid: true };
    }

    const error = validateInventory(inventory);
    if (!error.valid && error.errors) {
      if (error.errors.includes("Consumable ID is required ")) {
        handleMessage("Resource is not saved", "error");
        return;
      } else {
        handleMessage(error.errors[0], "error");
        return;
      }
    }
    // ...existing code...
    try {
      // const response = await axios.post(`/api/resources/${consumableData?.id}/inventory`, inventory)
      const response = await consumableService.post(
        `/resources/${consumableData?.id}/inventory`,
        inventory,
        token
      );
      if (response.status === 201) {
        fetchInventory(consumableData?.id!);
        setSupplierData({
          supplierResourceCode: "",
          quantity: "",
          unitPrice: "",
          addedOn: "",
          location: "",
        });
        // handleMessage("Your action was successfull! You can continue using the system without any interruption", "success")
      }
      // ...existing code...
    } catch (error) {
      console.error("Error uploading data");
      console.error(error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      // const response = await axios.delete(`/api/resources/inventory/${id}`)
      const response = await consumableService.delete(
        `/resources/inventory/${id}`,
        token
      );
      if (response.status === 200) {
        // ...existing code...
        fetchInventory(consumableData?.id!);
        handleMessage("Deleted ", "success");
      }
    } catch (error) {
      console.error("Error deleting data");
      console.error(error);
    }
  };

  useEffect(() => {
    if (id) {
      fetchInventory(id);
    }
  }, [id]);

  return (
    <div className="h-screen flex flex-col  overflow-y-auto bg-mainbg">
      <Header />
      {supplierModalStatus && (
        <AddSupplierModal
          handleStatus={(status: boolean) => handleSupplierModalStatus(status)}
          handleRefetch={() => setTrigger(trigger + 1)}
        />
      )}{" "}
      <ul className="flex gap-2 p-4">
        <Link to="/">
          <li>Home</li>
        </Link>
        <li>/</li>
        <Link to="/resources">
          <li>Resources</li>
        </Link>
      </ul>
      <div className="flex flex-col lg:flex-row gap-1">
        <div className="px-2 w-full lg:w-1/2">
          <GProductInfo
            id={id ? id : ""}
            setConsumableData={setConsumableData}
          />
        </div>
        {consumableData?.id || id ? (
          <div className="px-2 w-full lg:w-1/2 ">
            <GReceiptInfo
              handleDelete={handleDelete}
              message={message}
              supplierData={supplierData}
              supplier={supplier}
              setSupplier={setSupplier}
              inventory={inventory}
              addSupplierHandler={handleSupplierModalStatus}
              key={trigger}
              handleSupplierData={handleSupplierData}
              handleAddItem={handleAddItem}
            />
          </div>
        ) : (
          <div className="mx-2 border-2 w-full lg:w-1/2 flex items-center justify-center rounded-lg">
            <h1 className="text-2xl font-bold text-gray-500">
              Add a resource 
            </h1>
          </div>
        )}
      </div>
      {(consumableData?.id || id) && inventory && (
        <div className="px-2 mt-4 pb-20">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Receipts
              </h2>
            </div>
            <InventoryList
              inventory={inventory}
              handleDelete={handleDelete}
              formatDate={(dateString: string) => {
                const date = new Date(dateString);
                const day = String(date.getUTCDate()).padStart(2, "0");
                const month = String(date.getUTCMonth() + 1).padStart(2, "0");
                const year = date.getUTCFullYear();
                return `${day}-${month}-${year}`;
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Consumable;
