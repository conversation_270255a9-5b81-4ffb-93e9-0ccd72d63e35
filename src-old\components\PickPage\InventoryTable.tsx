
import { useEffect, useState } from "react";
import axios from "axios";
import { Plus } from "lucide-react";
import { useAuth } from "../../context/AuthContextProvider";
import { consumableService } from "../../services/api";
// import { cn } from "@/lib/utils";
import { CircularProgress } from "@mui/material";

interface IConsumableInventory {
  id: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  totalAvailableQuantity: number;
  totalAddedQuantity: number;
  averagePricePerUnit: number;
}

interface InventoryTableProps {
  onAddToCart: (product: IConsumableInventory) => void;
  searchWord: string
  type: string | undefined
}

const InventoryTable = ({ onAddToCart, searchWord,type }: InventoryTableProps) => {
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);
  const [consumables, setConsumables] = useState<IConsumableInventory[]>();
  const [filteredConsumables, setFilteredConsumables] = useState<IConsumableInventory[]>();
  const [loading, setLoading] = useState(true); // Add loading state
  const { token } = useAuth();

  const fetchConsumables: () => Promise<void> = async () => {
    setLoading(true); // Set loading to true when fetching starts
    try {
      const consumables = await consumableService.get("/resources/inventory", token);
      if (consumables.status === 200) {
        setFilteredConsumables(consumables.data);
        setConsumables(consumables.data);
      }
    } catch (error) {
      console.error("Error fetching consumables:", error);
    } finally {
      setLoading(false); // Set loading to false when fetching ends
    }
  };

  useEffect(() => {
    if (searchWord) {
      const filtered = consumables?.filter((data: any) =>
        data.resourceCode.toLowerCase().includes(searchWord.toLowerCase()) ||
        data.name.toLowerCase().includes(searchWord.toLowerCase())
      )
      setFilteredConsumables(filtered)
    } else {
      setFilteredConsumables(consumables)
    }
  }, [searchWord])  

  useEffect(() => {
    fetchConsumables()
  }, [])

  return (
    <div className="overflow-y-auto h-[calc(100vh-8rem)] border border-gray-200 rounded-2xl">
      {loading ? (
        <div className="flex justify-center items-center h-full">
          <CircularProgress color="primary" />
        </div>
      ) : (
        <table className="w-full border-collapse">
          <thead className="bg-tableHeaderBg h-9">
            <tr>
              <th className="bg-table-header text-xs  font-medium text-start px-4 border border-gray-300 w-2/12" >CODE</th>
              <th className="bg-table-header text-xs  font-medium text-start px-4 border border-gray-300" style={{width: "40%"}}>ITEM NAME</th>
              <th className="bg-table-header text-xs  font-medium text-start px-4 border border-gray-300 w-2/12" >QTY</th>
              <th className="bg-table-header text-xs  font-medium text-center  px-4 border border-gray-300 w-2/12" >Action</th>
            </tr>
          </thead>
          <tbody>
            {filteredConsumables?.map((consumable) => (
              <tr
                key={consumable.id}
                onMouseEnter={() => setHoveredRow(consumable.id)}
                onMouseLeave={() => setHoveredRow(null)}
                className="animate-fade-in hover:bg-gray-100 border-b border-gray-100 h-[4rem] pl-4 "
              >
                <td className="pl-4 text-sm" >{consumable.resourceCode}</td>
                <td className="pl-4 text-sm" >
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-10 h-10 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden">
                      <img
                        src={consumable.imageUrl}
                        alt={consumable.name}
                        className="w-full h-full object-contain p-1"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800 text-sm">
                        {consumable.name}
                      </p>
                    </div>
                  </div>
                </td>
                <td className="pl-4 text-sm" >{consumable.unitType === "pack" ? `${consumable.totalAvailableQuantity} EA` : consumable.totalAvailableQuantity}</td>
                <td className="pl-4 text-sm flex justify-center items-center h-16 " >
                  <button
                    className="bg-[#F6F7F9] text-gray-800 px-6 font-semibold py-2  text-sm flex items-center rounded-full border-black border hover:bg-[#E2E3E5]"
                    onClick={() => onAddToCart(consumable)}
                  >
                    <span className="mr-1 text-base"><Plus size={16} /></span>
                    {type === "pick" ? "Pick" : "Drop"}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default InventoryTable;
