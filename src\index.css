@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-bellBlack:#272727;
  --color-headingBlack:#303030;
  --color-roleTextBlue:#175CD3;
  --color-roleBgBlue:#EFF8FF;
  --color-dropArrow:#7F7F7F;
  --color-avatarBorderBlue:#DCEDFF;
  
  /* Font Family */
  --font-family-sans: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  
  /* Custom Primary Color Palette - #03A3E5 */
  --color-primary-50: #EBF5FF;
  --color-primary-100: #DCEDFF;
  --color-primary-200: #AFD9FF;
  --color-primary-300: #7FC8FF;
  --color-primary-400: #2EB8FF;
  --color-primary-500: #03A3E5;
  --color-primary-600: #0282B7;
  --color-primary-700: #006088;
  --color-primary-800: #00405D;
  --color-primary-900: #002437;
  --color-primary-950: #001522;

  /* Custom Secondary Color Palette - #FDC500 */
  --color-secondary-50: #FFF8EF;
  --color-secondary-100: #FFF2DF;
  --color-secondary-200: #FFE8C4;
  --color-secondary-300: #FFDB9A;
  --color-secondary-400: #FFD16F;
  --color-secondary-500: #FDC500;
  --color-secondary-600: #C79A00;
  --color-secondary-700: #917000;
  --color-secondary-800: #624A01;
  --color-secondary-900: #342600;
  --color-secondary-950: #201600;
  
  /* Custom Warning Color Palette - #F79009 */
  --color-warning-50: #FFFAEB;
  --color-warning-100: #FEF0C7;
  --color-warning-200: #FEDF89;
  --color-warning-300: #FEC84B;
  --color-warning-400: #FDB022;
  --color-warning-500: #F79009;
  --color-warning-600: #DC6803;
  --color-warning-700: #B54708;
  --color-warning-800: #93370D;
  --color-warning-900: #792E0D;
  --color-warning-950: #4E1902;
  
  /* Custom Green Color Palette - #059669 */
  --color-success-50: #ECFDF3;
  --color-success-100: #D1FADF;
  --color-success-200: #A6F4C5;
  --color-success-300: #6CE9A6;
  --color-success-400: #32D583;
  --color-success-500: #12B76A;
  --color-success-600: #039855;
  --color-success-700: #027A48;
  --color-success-800: #05603A;
  --color-success-900: #054F31;
  --color-success-950: #002D1B;
  
  /* Custom Red Color Palette - #F04438 */
  --color-error-50: #FDF1F1;
  --color-error-100: #FBDFDE;
  --color-error-200: #F7BEBD;
  --color-error-300: #F49C99;
  --color-error-400: #F17571;
  --color-error-500: #F04438;
  --color-error-600: #C0342A;
  --color-error-700: #92251D;
  --color-error-800: #661712;
  --color-error-900: #3E0A07;
  --color-error-950: #2C0504;
  
  /* Custom Grey Color Palette - #85999F */
  --color-grey-50: #EFF4F6;
  --color-grey-100: #DFEAEE;
  --color-grey-200: #C2D8DF;
  --color-grey-300: #A9C2CA;
  --color-grey-400: #98AFB6;
  --color-grey-500: #85999F;
  --color-grey-600: #6A7A7F;
  --color-grey-700: #6A7A7F;  
  --color-grey-800: #4D595D;
  --color-grey-900: #1B2022;
  --color-grey-950: #101415;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-family-sans: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }
}