import React from 'react';
import {
    Card,
    CardContent,
    Typography,
    Box,
} from '@mui/material';

interface Resource {
    id: string;
    name: string;
    resourceCode: string;
    unitType: string;
    unitSize: number;
    imageUrl: string;
    resourceCategory: string;
    minInventoryQty: number;
    maxInventoryQty: number;
    resourceType: string;
    deletedAt: string | null;
}

interface ResourceCost {
    resource: Resource;
    cost: number;
}

interface Project {
    id: string;
    projectCode: string;
    projectName: string;
    startDate: string;
    dueDate: string;
    createdAt: string;
    updatedAt: string;
}

interface ProjectCostsProps {
    data: {
        project: Project;
        totalCost: number;
        resourcesCost: ResourceCost[];
    }[];
    formatCurrency: (amount: number) => string;
}

const ProjectCosts: React.FC<ProjectCostsProps> = ({ data, formatCurrency }) => {
    return (
        <Card className="shadow-lg h-full flex flex-col">
            <CardContent className="p-3 flex flex-col h-full">
                <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                        <Typography variant="h6" className="text-white">🏗️</Typography>
                    </div>
                    <div>
                        <Typography variant="h6" className="font-semibold text-pickTBlue">
                            Cost by project
                        </Typography>
                        {/* <Typography variant="body2" color="textSecondary">
                            Top performing projects
                        </Typography> */}
                    </div>
                </div>                {data && data.length > 0 ? (
                    <Box className="space-y-2 overflow-y-auto flex-grow" style={{ maxHeight: 'calc(100% - 60px)', overflowY: 'auto' }}>
                        {data.map((projectData, index) => (
                            <Box key={index} className="bg-secondarybg p-3 rounded-lg border border-gray-200">
                                <Typography variant="body1" className="font-bold text-green-600 mb-2">
                                    {formatCurrency(projectData.totalCost)}
                                </Typography>
                                <Typography variant="body1" className="font-semibold text-gray-800 mb-1">
                                    {projectData.project.projectName}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                    {projectData.project.projectCode}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                ) : (
                    <Box className="text-center py-6 bg-gray-50 rounded-lg border border-gray-200">
                        <Typography variant="body2" color="textSecondary" className="mb-1">
                            🏗️ No project data available
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                            Project costs will be displayed here
                        </Typography>
                    </Box>
                )}
            </CardContent>
        </Card>
    );
};

export default ProjectCosts;
