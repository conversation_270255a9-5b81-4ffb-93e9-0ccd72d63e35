import React from 'react';
import { Upload } from 'lucide-react';

const UploadPrompt: React.FC = () => {
    return (
        <div className='flex flex-col justify-center items-center'>
            <Upload className="h-6 w-6 text-gray-500 mb-2" />
            <label className="text-sm text-gray-600 cursor-pointer text-center hover:text-gray-800">
                Click to upload
            </label>
        </div>
    );
};

export default UploadPrompt;
