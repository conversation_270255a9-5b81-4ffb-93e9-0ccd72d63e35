import { useState, useMemo } from 'react';

interface UsePaginationProps<T> {
  data: T[];
  itemsPerPage: number;
}

interface UsePaginationReturn<T> {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  startIndex: number;
  endIndex: number;
  currentData: T[];
  setCurrentPage: (page: number) => void;
  handlePrevious: () => void;
  handleNext: () => void;
  canGoPrevious: boolean;
  canGoNext: boolean;
}

export const usePagination = <T>({
  data,
  itemsPerPage
}: UsePaginationProps<T>): UsePaginationReturn<T> => {
  const [currentPage, setCurrentPage] = useState(1);

  const paginationData = useMemo(() => {
    const totalItems = data.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentData = data.slice(startIndex, endIndex);

    return {
      totalItems,
      totalPages,
      startIndex,
      endIndex,
      currentData
    };
  }, [data, itemsPerPage, currentPage]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < paginationData.totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const canGoPrevious = currentPage > 1;
  const canGoNext = currentPage < paginationData.totalPages;

  return {
    currentPage,
    setCurrentPage,
    handlePrevious,
    handleNext,
    canGoPrevious,
    canGoNext,
    ...paginationData
  };
};
