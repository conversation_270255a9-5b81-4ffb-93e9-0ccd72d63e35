import React from 'react';
import { ISupplier } from '../models/Supplier';

interface SupplierListProps {
  suppliers: ISupplier[];
}

const SupplierList: React.FC<SupplierListProps> = ({ suppliers }) => {
  return (
    <div className=''>
      <h1 className='text-4xl font-bold text-start mb-4'>Suppliers</h1>
      <ul className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
        {suppliers.map(supplier => (
          <li key={supplier.id} className='flex flex-col gap-3 border border-black p-5 rounded-xl justify-between items-start'>
            <h2 className='text-lg font-bold'>{supplier.name}</h2>
            <p><span className='font-semibold'>Code:</span> {supplier.code}</p>
            <p><span className='font-semibold'>Email:</span> {supplier.contactEmail}</p>
            <p><span className='font-semibold'>Phone:</span> {supplier.phoneNumber}</p>
            <p><span className='font-semibold'>Address: </span> {supplier.address}</p>
            <p><span className='font-semibold'>Contact Person: </span> {supplier.contactPerson}</p>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SupplierList;
