import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ResourceManagementPage from '../../../src/modules/resources/ResourceManagementPage';
import * as useResources from '../../../src/modules/resources/hooks/use-resources';

import { UseQueryResult } from '@tanstack/react-query';

describe('ResourceManagementPage', () => {
  const mockUseResources = (
    options: Partial<UseQueryResult<any, Error>>
  ): UseQueryResult<any, Error> =>
    ({
      data: [],
      error: null,
      isError: false,
      isLoading: false,
      isLoadingError: false,
      isPlaceholderData: false,
      isRefetchError: false,
      isRefetching: false,
      isStale: false,
      isSuccess: true,
      refetch: vi.fn(),
      status: 'success',
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      errorUpdateCount: 0,
      isFetched: true,
      isFetchedAfterMount: true,
      isFetching: false,
      isInitialLoading: false,
      isPaused: false,
      ...options,
    } as UseQueryResult<any, Error>);

  it('renders loading state initially', () => {
    vi.spyOn(useResources, 'useResources').mockReturnValue(
      mockUseResources({ isLoading: true, status: 'pending' })
    );

    render(<ResourceManagementPage />);
    expect(screen.getByText('Loading resources...')).toBeInTheDocument();
  });

  it('renders error state', () => {
    vi.spyOn(useResources, 'useResources').mockReturnValue(
      mockUseResources({
        error: new Error('Failed to fetch'),
        isError: true,
        status: 'error',
      })
    );

    render(<ResourceManagementPage />);
    expect(screen.getByText('Error: Failed to fetch')).toBeInTheDocument();
  });

  it('renders the resource table with data', () => {
    const mockData = [
      {
        id: '1',
        name: 'Resource 1',
        quantity: 10,
        price: 100,
        category: 'Category 1',
        brand: 'Brand 1',
        type: 'Type 1',
      },
      {
        id: '2',
        name: 'Resource 2',
        quantity: 20,
        price: 200,
        category: 'Category 2',
        brand: 'Brand 2',
        type: 'Type 2',
      },
    ];

    vi.spyOn(useResources, 'useResources').mockReturnValue(
      mockUseResources({ data: mockData })
    );

    render(<ResourceManagementPage />);

    expect(screen.getByText('Resource Management')).toBeInTheDocument();
    // Note: Since ResourceTable is complex, we're just checking for the header.
    // A more thorough test would involve testing the ResourceTable component itself.
  });
});
