import React from 'react';
import { Modal, Box } from "@mui/material";

interface ResetPasswordModalProps {
  open: boolean;
  onClose: () => void;
  onReset: () => void;
  resetPasswordResponse: {
    password?: string;
    message?: string;
  } | null;
  onConfirmClose: () => void;
  showCloseConfirmation: boolean;
  onCancelConfirmation: () => void;
  onConfirmAndClose: () => void; // New function to handle both closing confirmation and primary modal
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  open,
  onClose,
  onReset,
  resetPasswordResponse,
  onConfirmClose,
  showCloseConfirmation,
  onCancelConfirmation,
  onConfirmAndClose
})=> {
  return (
    <>
      <Modal
        open={open}
        onClose={() => {
          if (resetPasswordResponse) {
            onConfirmClose();
          } else {
            onClose();
          }
        }}
        aria-labelledby="reset-password-modal"
      >
        <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[90%] max-w-md flex flex-col gap-4">
          <h3 className="text-lg font-semibold">Reset Password</h3>
          {resetPasswordResponse ? (
            <div>
              <div className="mb-4">
                {resetPasswordResponse?.password && (
                  <div className="text-center">
                    <p className="text-gray-900 font-mono font-bold text-2xl tracking-wider">
                      {resetPasswordResponse.password}
                    </p>
                  </div>
                )}
              </div>
              
              {/* Display explanation if no password is shown */}
              {!resetPasswordResponse?.password && (
                <div className="mt-2 text-red-600">
                  The temporary password could not be retrieved from the server response.
                  Please try again.
                </div>
              )}
              <div className="flex justify-end gap-3 mt-4">
                <button
                  onClick={onConfirmClose}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Close
                </button>
              </div>
            </div>
          ) : (
            <>
              <p>
                Are you sure you want to reset this user's password? A new password will be generated.
              </p>
              <div className="flex justify-end gap-3 mt-2">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
                >
                  Cancel
                </button>
                <button
                  onClick={onReset}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Reset Password
                </button>
              </div>
            </>
          )}
        </Box>
      </Modal>

      {/* Close Confirmation Modal */}
      <Modal
        open={showCloseConfirmation}
        onClose={onCancelConfirmation}
        aria-labelledby="close-confirmation-modal"
      >
        <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[90%] max-w-md flex flex-col gap-4">
          <h3 className="text-lg font-semibold">Confirmation</h3>
          <div>
            <p className="text-gray-800">
              This password won't be shown again after closing. Are you sure you want to close?
            </p>
          </div>
          <div className="flex justify-end gap-3 mt-2">
            <button
              onClick={onCancelConfirmation}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
            >
              Cancel
            </button>            <button
              onClick={onConfirmAndClose}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Yes, Close
            </button>
          </div>
        </Box>
      </Modal>
    </>
  );
};

export default ResetPasswordModal;
