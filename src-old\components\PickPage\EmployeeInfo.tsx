import { Autocomplete, TextField } from "@mui/material";
import { useAuth } from "../../context/AuthContextProvider";
import { useEffect, useState } from "react";
import { projectService } from "../../services/api";
import { hasPermission } from "../../utils/permissionUtils";

interface IUser {
  id: string;
  employeeId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  roles: string[];
  isActive: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

interface IProject {
  id: string;
  projectCode: string;
  projectName: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EmployeeInfoProps {
  projectId: string | null;
  selectedUser: IUser | null;
  setSelectedUser: (user: IUser | null) => void;
  handlePojectId: (projectId: string) => void;
  users: IUser[];
  type: string | undefined;
}

const EmployeeInfo = ({ type, projectId, selectedUser, setSelectedUser, handlePojectId, users }: EmployeeInfoProps) => {
  const { user, token } = useAuth();
  const [projects, setProjects] = useState<IProject[]>([]);
  
  // Check if user has permission to pick for others
  const canPickForOthers = hasPermission("pick-for-others");

  // Fetch projects when component mounts
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await projectService.get('/projects', token);
        setProjects(response.data);
      } catch (err) {
        console.error('Error fetching projects:', err);
      }
    };
    
    fetchProjects();
  }, [token]);
  // Auto-select the current user when component mounts or when users/user changes
  useEffect(() => {
    if (user && users.length > 0 && !selectedUser && !canPickForOthers) {
      const currentUser = users.find((u) => u.id === user.sub);
      if (currentUser) {
        setSelectedUser(currentUser);
      }
    }
  }, [user, users, selectedUser, setSelectedUser, canPickForOthers]);


  const handleProjectChange = (event: React.SyntheticEvent, value: IProject | null) => {
    handlePojectId(value ? value.id : '');
  };
  return (
    <div className="font-nunito ">
      {/* <h2 className="mb-1 font-semibold">
        Employee Information
      </h2> */}
      <div className=" animate-slide-up bg-[#E7F4FF] rounded-xl border border-[#E6E6E6] py-1">
        <div className="flex items-center gap-3  px-3 py-1">          <div className="w-full ">
            <div className="relative w-full ">
              <label htmlFor="" className="block text-sm font-medium text-gray-700">{type === "pick" ? "Picked by" : "Drop by"} <sup className="text-red-500">*</sup></label>
              {canPickForOthers ? (
                <Autocomplete
                  id="userId"
                  options={users}
                  getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
                  value={selectedUser || null}
                  onChange={(event, value) => setSelectedUser(value)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="mt-1 block w-full rounded-md text-sm border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 outline-none"
                      placeholder="Select a user"
                      size="small"
                      sx={{
                        "& .MuiInputBase-root": {
                          padding: 0,
                          fontSize: '0.8rem'
                        },
                        "& .MuiInputBase-input": {
                          height: "1em",
                          padding: "8.5px 4px",
                        },
                      }}
                    />
                  )}
                />
              ) : (
                <div className="font-semibold text-sm w-full border rounded-md p-1 bg-gray-50">
                  {selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName}` : 'Loading...'}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="px-3 pb-2  mt-1">
          <label htmlFor="" className="block text-sm font-medium text-gray-700 w-2/5">Project ID <sup className="text-red-500">*</sup></label>
          <Autocomplete
            id="projectId"
            options={projects}
            getOptionLabel={(option) => `${option.projectCode} - ${option.projectName}`}
            value={projects.find((p) => p.id === projectId) || null}
            onChange={handleProjectChange}
            renderInput={(params) => (
              <TextField
                {...params}
                size="small"
                className="mt-1 block w-full rounded-md text-sm border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 outline-none"
                placeholder="Select a project"
                sx={{
                  "& .MuiInputBase-root": {
                    padding: 0,
                    fontSize: '0.8rem'
                  },
                  "& .MuiInputBase-input": {
                    height: "1em",
                    padding: "8.5px 4px",
                  },
                }}
              />
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default EmployeeInfo;
