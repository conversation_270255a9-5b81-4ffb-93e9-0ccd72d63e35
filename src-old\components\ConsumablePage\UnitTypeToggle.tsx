import React, { useEffect, useState } from 'react'

interface IUnitTypeToggele {
    toggle: (value: string) => void
    isEditable: boolean
    isPack: boolean
    setIsPack: (value: boolean) => void
    error?: string[] 

}

const UnitTypeToggle = ({ toggle, isEditable, isPack, setIsPack, error }: IUnitTypeToggele) => {
    // const [isPack, setIsPack] = useState(value);
    const toggleState = () => {
        if (!isEditable) {
            return
        }
        if (isPack) {
            toggle("individual")
        } else {
            toggle("pack")
        }
        setIsPack(!isPack);
    };

    return (
        <div className={`flex flex-col gap-2 h-full font-nunito`}>
            <label className='text-sm font-medium'>Unit Type <span className="text-red-500">*</span></label>
            <div
                className="relative flex w-60 h-10 rounded-full cursor-pointer overflow-hidden shadow-md"
                onClick={toggleState}
            >
                {/* Base Background */}
                <div className="absolute inset-0 bg-white  "></div>

                {/* Slider (Black Background for Selected Option) */}
                <div
                    className={`absolute top-0 bottom-0 w-1/2 bg-black transition-transform duration-500 ease-in-out ${isPack ? 'translate-x-full' : 'translate-x-0'
                        }`}
                ></div>

                {/* Text Labels */}
                <div className="relative flex items-center justify-between w-full z-10  border rounded-full">
                    <div className="flex-1 text-center transition-colors duration-500 ease-in-out ">
                        <span className={`font-medium ${!isPack ? 'text-white' : 'text-gray-800'}`}>
                            INDIVIDUAL
                        </span>
                    </div>
                    <div className="flex-1 text-center transition-colors duration-500 ease-in-out">
                        <span className={`font-medium ${isPack ? 'text-white' : 'text-gray-800'}`}>
                            PACK
                        </span>
                    </div>
                </div>
            </div>
            <div className={`${error ? "block" : "opacity-0"}`}>
                {
                    error?.map(err => {
                        if (err.length > 0) {
                            return <label className={`text-xs text-red-500 `}>{err}</label>
                        }
                    })
                }
            </div>
            {/* <label className={`${error ? "block" : "hidden"} text-xs text-red-500 `}>{typeof (error) != "string" && error?.[0]}</label> */}


        </div>

    );
};



export default UnitTypeToggle

