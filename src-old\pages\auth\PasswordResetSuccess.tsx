import React from 'react';
import { useNavigate } from 'react-router-dom';

const PasswordResetSuccess = () => {
    const navigate = useNavigate();

    const handleLoginRedirect = () => {
        navigate('/login');
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 font-nunito">
            <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md text-center">
                {/* Success Icon */}
                <div className="mx-auto mb-6 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <svg 
                        className="w-8 h-8 text-green-600" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M5 13l4 4L19 7" 
                        />
                    </svg>
                </div>

                {/* Success Message */}
                <h1 className="text-2xl font-bold mb-4 text-gray-800">Password Reset Successful!</h1>
                <p className="text-gray-600 mb-8">
                    Your password has been successfully updated. You can now login with your new password.
                </p>

                {/* Login Button */}
                <button
                    onClick={handleLoginRedirect}
                    className="w-full bg-pickTBlue text-white py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                >
                    Click to Login
                </button>
            </div>
        </div>
    );
};

export default PasswordResetSuccess;
