import { Button } from "@/components/ui/button";
import { Loader2, Plus } from "lucide-react";

interface UserManagementHeaderProps {
  setIsAddModalOpen: (isOpen: boolean) => void;
  isCreating: boolean;
}

const UserManagementHeader = ({
  setIsAddModalOpen,
  isCreating,
}: UserManagementHeaderProps) => {
  return (
    <div className="mb-6 flex items-start justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          User Management
        </h1>
        <p className="text-gray-600">
          Manage Admins, Supervisors, and Mechanics. Add, edit, reset passwords,
          or deactivate users.
        </p>
      </div>
      <Button
        className="bg-blue-600 hover:bg-blue-700"
        onClick={() => setIsAddModalOpen(true)}
        disabled={isCreating}
      >
        {isCreating ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Plus className="h-4 w-4 mr-2" />
        )}
        Add New User
      </Button>
    </div>
  );
};

export default UserManagementHeader;
