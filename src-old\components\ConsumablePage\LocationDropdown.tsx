import React, { useState, useEffect } from 'react';
import { resourceService } from '../../services/api';

interface LocationDropdownProps {
  value?: string;
  onChange: (location: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const LocationDropdown: React.FC<LocationDropdownProps> = ({
  value,
  onChange,
  placeholder = "Select a location",
  disabled = false,
  className = ""
}) => {
  const [locations, setLocations] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    fetchLocations();
  }, []);

  const fetchLocations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get token from localStorage
      const token = localStorage.getItem('token');
      
      const response = await resourceService.get('/resources/locations', token);
      
      if (response.data && Array.isArray(response.data)) {
        setLocations(response.data);
      } else {
        setError('Invalid response format');
      }
    } catch (err: any) {
      console.error('Error fetching locations:', err);
      setError(err.response?.data?.message || 'Failed to fetch locations');
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = (location: string) => {
    onChange(location);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <div
        className={`border rounded-md h-11 flex items-center px-3 text-sm font-normal cursor-pointer bg-white ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-gray-400'
        } ${isOpen ? 'border-blue-500' : 'border-gray-300'}`}
        onClick={() => !disabled && !loading && setIsOpen(!isOpen)}
      >
        {loading ? (
          <span className="text-gray-500">Loading...</span>
        ) : error ? (
          <span className="text-red-500">Error loading locations</span>
        ) : value ? (
          <span>{value}</span>
        ) : (
          <span className="text-gray-500">{placeholder}</span>
        )}
        
        {!disabled && !loading && (
          <svg
            className={`w-4 h-4 ml-auto transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </div>

      {isOpen && !disabled && !loading && !error && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          {locations.length === 0 ? (
            <div className="px-3 py-2 text-gray-500 text-sm">No locations available</div>
          ) : (
            locations.map((location) => (
              <div
                key={location}
                className="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={() => handleSelect(location)}
              >
                <div className="font-medium">{location}</div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Backdrop to close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default LocationDropdown;
