import { render, screen, waitFor } from '@testing-library/react';
import { act } from 'react';
import '@testing-library/jest-dom/extend-expect';
import SupplierManagement from './SupplierManagement';

describe('SupplierManagement Component', () => {
  beforeEach(() => {
    // Mock the fetch API
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve([{ id: 1, name: 'Supplier 1', code: 'S001', contactEmail: '<EMAIL>', phoneNumber: '1234567890', address: '123 Supplier St', contactPerson: '<PERSON>' }]),
      } as Response)
    );
    (fetch as jest.Mock).mockClear();
  });

  test('renders SupplierManagement component', async () => {
    await act(async () => {
      render(<SupplierManagement />);
    });
    expect(screen.getByText('Supplier Management')).toBeInTheDocument();
  });

  test('fetches and displays suppliers', async () => {
    await act(async () => {
      render(<SupplierManagement />);
    });
    await waitFor(() => expect(fetch).toHaveBeenCalledTimes(1));
    await waitFor(() => {
      const supplierElement = screen.getByText('Supplier 1');
      expect(supplierElement).toBeInTheDocument();
    });
  });

  test('handles supplier creation', async () => {
    await act(async () => {
      render(<SupplierManagement />);
    });
    // Simulate supplier creation
    await waitFor(() => expect(fetch).toHaveBeenCalledTimes(1));
    // Assuming CreateSupplier component triggers fetch on creation
    // This part would need to simulate the creation event
  });
});
