import React, { useState, useEffect } from 'react';
import { consumableService } from '../../services/api';
import { useAuth } from '../../context/AuthContextProvider';
import { CircularProgress, Modal, Box } from '@mui/material';
import { Minus, MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { toast } from 'react-hot-toast';

// Add interface for transactions
export interface ITransaction {
    id: string;
    resourceName: string; 
    resourceId: string;
    quantity: number;
    effectiveQuantity: number;
    unitType: string;
    actionType: string;
    userName: string;
    userId: string;
    timestamp: Date | string;
    project?: {
        id: string;
        projectCode: string;
        projectName: string;
    };
    resourceImageUrl: string;
    childTransactions: ITransaction[];
}



interface DropTableProps {
    searchWord: string;
    onDropItem?: (transaction: ITransaction) => void;
}

const DropTable: React.FC<DropTableProps> = ({ searchWord, onDropItem }) => {
    const [hoveredRow, setHoveredRow] = useState<string | null>(null);
    const [filteredDroppableConsumables, setFilteredDroppableConsumables] = useState<ITransaction[]>([]);
    const [loading, setLoading] = useState(true);
    const { token } = useAuth();
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedTransaction, setSelectedTransaction] = useState<ITransaction | null>(null);
    const [dropQuantity, setDropQuantity] = useState(1);
    // Add new state for tracking unit type
    const [selectedUnitType, setSelectedUnitType] = useState<string>("pack");

    // ...existing code...
    
    // Add validation for drop quantity
    const validateDropQuantity = (quantity: number) => {
        if (!selectedTransaction) return;
        
        if (quantity <= 0) {
            return false;
        }
        
        if (quantity > selectedTransaction.effectiveQuantity) {
            return false;
        }
        
        return true;
    };

    useEffect(() => {
        const fetchConsumablesWithTransactions = async () => {
            try {
                setLoading(true);
                const response = await consumableService.get("/resources/inventory/transactions?actionType=pick", token);
                // ...existing code...
                setFilteredDroppableConsumables(response.data.transactions);

            } catch (error) {
                console.error("Error fetching transactions:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchConsumablesWithTransactions();
    }, [token]);

    // Filter consumables based on search word
    const displayConsumables = filteredDroppableConsumables?.filter((consumable) =>
        consumable.resourceName.toLowerCase().includes(searchWord.toLowerCase()) ||
        consumable.resourceId.toLowerCase().includes(searchWord.toLowerCase())
    );

    const handleDropClick = (transaction: ITransaction) => {
        setSelectedTransaction(transaction);
        setDropQuantity(1); // Reset to 1 for each new transaction
        setSelectedUnitType(transaction.unitType); // Initialize with transaction's unit type
        setModalOpen(true);
    };

    const handleConfirmDrop = () => {
        if (selectedTransaction && onDropItem && validateDropQuantity(dropQuantity)) {
            // Create a modified transaction with the user-specified quantity and unit type
            const modifiedTransaction = {
                ...selectedTransaction,
                effectiveQuantity: dropQuantity,
                // unitType: selectedUnitType // Use the selected unit type
            };
            onDropItem(modifiedTransaction);
            setModalOpen(false);
            
            // Show success toast notification
            // toast.success(`Successfully dropped ${dropQuantity} ${selectedUnitType === "pack" ? "pack" : "individual"} of ${selectedTransaction.consumableName}`);
        }
    };

    return (
        <>
            <div className="overflow-y-auto h-[calc(100vh-8rem)] border border-gray-200 rounded-2xl">
                {loading ? (
                    <div className="flex justify-center items-center h-full">
                        <CircularProgress color="primary" />
                    </div>
                ) : (
                    <table className="w-full border-collapse">
                        <thead className="bg-tableHeaderBg h-9">
                            <tr>
                                <th className="bg-table-header text-xs font-medium text-start px-4 border border-gray-300" style={{ width: "20%" }}>ITEM NAME</th>
                                <th className="bg-table-header text-xs font-medium text-start px-4 border border-gray-300 w-1/12">QTY</th>
                                <th className="bg-table-header text-xs font-medium text-start px-4 border border-gray-300 w-1/12">UNIT TYPE</th>
                                <th className="bg-table-header text-xs font-medium text-start px-4 border border-gray-300 w-1/12">PICKED BY</th>
                                <th className="bg-table-header text-xs font-medium text-start px-4 border border-gray-300 w-1/12">PROJECT ID</th>
                                <th className="bg-table-header text-xs font-medium text-center px-4 border border-gray-300 w-1/12">ACTION</th>
                            </tr>
                        </thead>
                        <tbody>
                            {displayConsumables?.map((transaction) => (
                                <tr
                                    key={transaction.id}
                                    onMouseEnter={() => setHoveredRow(transaction.id)}
                                    onMouseLeave={() => setHoveredRow(null)}
                                    className="animate-fade-in hover:bg-gray-100 border-b border-gray-100 h-[4rem] pl-4"
                                >
                                    <td className="pl-4 text-sm">
                                        <div className="flex items-center gap-3 text-sm">
                                            <div className="w-10 h-10 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden">
                                                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                                    <img
                                                        src={transaction?.resourceImageUrl}
                                                        alt={transaction.resourceName}
                                                        className="w-full h-full object-contain p-1"
                                                    />
                                                </div>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-800 text-sm">
                                                    {transaction.resourceName}
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="pl-4 text-sm">
                                        {transaction.unitType === "pack" ? `${transaction.effectiveQuantity} EA` : transaction.effectiveQuantity}
                                    </td>
                                    <td className="pl-4 text-sm">
                                        { transaction.unitType}
                                    </td>
                                    <td className="pl-4 text-sm">
                                        {transaction.userName || '-'}
                                    </td>
                                    <td className="pl-4 text-sm">
                                        {transaction.project?.projectCode || '-'}
                                    </td>
                                    <td className="pl-4 text-sm text-center">
                                        <div className='flex justify-center'>
                                            <button
                                                className="bg-[#F6F7F9] text-gray-800 px-4 font-semibold py-2 text-sm flex items-center rounded-full border-black border hover:bg-[#E2E3E5]"
                                                onClick={() => handleDropClick(transaction)}
                                            >
                                                <span className="mr-1 text-base"><Minus size={16} /></span>
                                                Drop
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>

            {/* Drop Quantity Modal */}
            <Modal
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                aria-labelledby="drop-quantity-modal"
            >
                <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[90%] max-w-md flex flex-col gap-4 items-center text-center">
                    <h3 className="text-lg font-semibold">Drop Item</h3>
                    <p className="text-sm">
                        {selectedTransaction?.resourceName} - Picked: {selectedTransaction?.effectiveQuantity} individual
                    </p>
                    
                    {/* {selectedTransaction?.unitType === "pack" && (
                        <div className="flex items-center gap-2 w-full px-5 mb-2">
                            <button
                                onClick={() => setSelectedUnitType("pack")}
                                className={`px-2 py-1 w-1/2 border rounded-lg ${selectedUnitType === "pack" ? "bg-gray-200" : ""}`}
                            >
                                Pack
                            </button>
                            <button
                                onClick={() => setSelectedUnitType("individual")}
                                className={`px-2 py-1 w-1/2 border rounded-lg ${selectedUnitType === "individual" ? "bg-gray-200" : ""}`}
                            >
                                Individual
                            </button>
                        </div>
                    )} */}
                    
                    <div className="flex flex-col gap-2 items-center">
                        <label htmlFor="dropQuantity" className="text-sm font-medium">
                            Quantity to drop:
                        </label>
                        
                        <div className="flex items-center justify-center">
                            <button 
                                onClick={() => {
                                    if (dropQuantity > 1) {
                                        const newQuantity = Math.max(1, dropQuantity - 1);
                                        setDropQuantity(newQuantity);
                                        validateDropQuantity(newQuantity);
                                    }
                                }}
                                className="w-7 h-7 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={dropQuantity <= 1}
                            >
                                <MinusCircleIcon className="hover:bg-[#E2E3E5]"/>
                            </button>
                            <span className="w-6 text-center text-base font-bold text-pickTBlue">{dropQuantity}</span>
                            <button 
                                onClick={() => {
                                    if (dropQuantity < (selectedTransaction?.effectiveQuantity || 1)) {
                                        const newQuantity = Math.min(dropQuantity + 1, selectedTransaction?.effectiveQuantity || 1);
                                        setDropQuantity(newQuantity);
                                        validateDropQuantity(newQuantity);
                                    }
                                }}
                                className="w-7 h-7 text-gray-600"
                                disabled={dropQuantity >= (selectedTransaction?.effectiveQuantity || 1)}
                            >
                                <PlusCircleIcon className=""/>
                            </button>
                        </div>
                    </div>
                    
                    <div className="flex justify-center gap-3 mt-2">
                        <button 
                            onClick={() => setModalOpen(false)}
                            className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
                        >
                            Cancel
                        </button>
                        <button 
                            onClick={handleConfirmDrop}
                            className="px-4 py-2 text-sm bg-pickTBlue text-white rounded-md hover:bg-blue-600"
                        >
                            Confirm
                        </button>
                    </div>
                </Box>
            </Modal>
        </>
    );
};

export default DropTable
