import { useMemo } from 'react';

interface UsePaginationNumbersProps {
  currentPage: number;
  totalPages: number;
  showPages?: number;
}

export const usePaginationNumbers = ({
  currentPage,
  totalPages,
  showPages = 5
}: UsePaginationNumbersProps): (number | string)[] => {
  return useMemo(() => {
    const pages: (number | string)[] = [];

    if (totalPages <= showPages) {
      // Show all pages if total pages is less than or equal to showPages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages with ellipsis
      if (currentPage <= 3) {
        // Show first 3 pages, ellipsis, last 2 pages
        for (let i = 1; i <= 3; i++) {
          pages.push(i);
        }
        if (totalPages > 4) {
          pages.push("...");
          pages.push(totalPages - 1, totalPages);
        }
      } else if (currentPage >= totalPages - 2) {
        // Show first 2 pages, ellipsis, last 3 pages
        pages.push(1, 2);
        if (totalPages > 4) {
          pages.push("...");
        }
        for (let i = totalPages - 2; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show first page, ellipsis, current-1, current, current+1, ellipsis, last page
        pages.push(1);
        pages.push("...");
        pages.push(currentPage - 1, currentPage, currentPage + 1);
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  }, [currentPage, totalPages, showPages]);
};
