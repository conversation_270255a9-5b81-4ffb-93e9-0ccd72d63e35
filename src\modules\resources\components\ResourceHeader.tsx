import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import AddOrEditResourceDrawer from "./AddOrEditResourceDrawer";

interface ResourceHeaderProps {
  onResourceUpdate: () => void;
}

const ResourceHeader = ({ onResourceUpdate }: ResourceHeaderProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight mb-2">
            Manage your resources
          </h2>
          <p className="text-muted-foreground">
            All your workshop tools and materials live here. Add and manage them
            effortlessly.
          </p>
        </div>
        <Button
          className="bg-primary-500 hover:bg-primary-600 h-10 text-lg px-6 flex items-center justify-center"
          onClick={() => setIsDrawerOpen(true)}
        >
          <Plus className="h-6 w-6 text-white" />
          Add new resource
        </Button>
      </div>

<AddOrEditResourceDrawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        onResourceUpdate={onResourceUpdate}
      />
    </>
  );
};

export default ResourceHeader;
