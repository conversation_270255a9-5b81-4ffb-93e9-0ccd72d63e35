import React from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Fade
} from '@mui/material';
import { ReportItem } from '../../models/Report';

interface ProjectConsumableReportProps {
  reportData: ReportItem[];
  loading: boolean;
  error: string | null;
}

const ProjectConsumableReport: React.FC<ProjectConsumableReportProps> = ({
  reportData,
  loading,
  error
}) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <Fade in={!loading}>
      <TableContainer component={Paper}>
        <Table>
          <TableHead className="bg-tableHeaderBg">
            <TableRow>
              <TableCell className="text-xs"><strong>Project</strong></TableCell>
              <TableCell className="text-xs"><strong>Consumable</strong></TableCell>
              <TableCell className="text-xs"><strong>Image</strong></TableCell>
              <TableCell className="text-xs"><strong>Quantity</strong></TableCell>
              <TableCell className="text-xs"><strong>Unit Type</strong></TableCell>
              <TableCell className="text-xs"><strong>Amount</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {reportData.length > 0 ? (
              reportData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="text-sm">
                    {item.project?.projectName || 'N/A'} {item.project?.projectCode ? `(${item.project.projectCode})` : ''}
                  </TableCell>
                  <TableCell className="text-sm">{item.consumableName || 'N/A'}</TableCell>
                  <TableCell className="text-sm">
                    {item.consumableImageUrl ? (
                      <div className="w-[50px] h-[50px] border border-gray-200 rounded overflow-hidden">
                        <img 
                          src={item.consumableImageUrl} 
                          alt={item.consumableName}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-[50px] h-[50px] bg-gray-100 flex items-center justify-center text-gray-400 text-xs">No image</div>
                    )}
                  </TableCell>
                  <TableCell className="text-sm">{item.effectiveQuantity}</TableCell>
                  <TableCell className="text-sm">{item.unitType}</TableCell>
                  <TableCell className="text-sm">${((item.totalPrice || 0) * 1).toFixed(2)}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center" className="text-sm">No data available</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Fade>
  );
};

export default ProjectConsumableReport;
