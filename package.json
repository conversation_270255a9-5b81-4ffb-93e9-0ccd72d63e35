{"name": "picktrail-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.10", "react-router-dom": "^6.30.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4"}}