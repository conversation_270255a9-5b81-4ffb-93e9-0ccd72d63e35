import React, { ReactElement } from 'react'
import { useAuth } from '../context/AuthContextProvider'
import { Navigate } from 'react-router-dom'

const ProtectedRouteForAdmin = ({ children }: { children: React.ReactNode }): ReactElement => {
    const { user, loading } = useAuth()
    // console.log("user", user, "loading", loading)
    
    // Don't redirect while authentication is being checked
    if (loading) {
        return <div>Loading...</div> // Or a proper loading component
    }
    
    if (!user) {
        return <Navigate to={'/login'} />
    }
      return (user.roles.includes('admin') || user.roles.includes('mechanic') || user.roles.includes('supervisor')) ? (
        <>{children}</>
    ) : (
        <Navigate to={'/login'} />
    )
}

export default ProtectedRouteForAdmin
