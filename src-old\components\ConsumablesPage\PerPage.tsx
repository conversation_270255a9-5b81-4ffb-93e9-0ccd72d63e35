import React from 'react'

interface IPerPage {
    setItemsPerPage: (e: number) => void
}

const PerPage = ({ setItemsPerPage }: IPerPage) => {
    return (
        <div className='flex gap-2 items-center font-normal text-sm font-inter'>
            <h3>Result per page</h3>
            <select name="" id="" className='py-2 px-4 rounded-md outline-none' onChange={(e) => setItemsPerPage(parseInt(e.target.value))}>
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="25" selected={true}>25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
    )
}

export default PerPage