
import { MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { useState } from "react";
import DeleteIcon from '@mui/icons-material/Delete';
import { Modal, Box } from '@mui/material';
import toast from 'react-hot-toast';

type CartItem = {
  id: string;
  name: string;
  image: string;
  unitType: string;
  turnedTo: string;
  quantity: number;
  avalableQuantity: number;
  totalAddedQuantity: number;
};

interface PickListProps {
  items: CartItem[];
  onQuantityChange: (id: string, quantity: number, availableQuantity: number, totalAddedQuantity:number, unitType?: string) => void;
  onRemoveItem: (id: string) => void;
  onPickAll: () => void;
  onCancel: () => void;
  type: string | undefined
  error: string | null;
}

const PickList = ({
  items,
  onQuantityChange,
  onRemoveItem,
  onPickAll,
  onCancel,
  type,
  error
}: PickListProps) => {
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  
  const handleConfirm = () => {
    setConfirmModalOpen(false);
    onPickAll();
    // Remove the toast from here - it will be shown in the parent component after successful operation
  };
  // ...existing code...
  return (
    <div className="h-full">
      <div className="flex justify-between">
        <h2 className="font-semibold mb-1">
          {type === "pick" ? "Pick" : "Drop"} checkout list
        </h2>
        <p className="text-sm text-red-500 bg-red-200 h-fit px-2 rounded-full">
          {error ? error : ""}
        </p>
      </div>
      <div className="flex flex-col  gap-2  h-full ">
        <div className="overflow-y-auto h-[calc(100vh-20rem)]   bg-[#F2F4F7] rounded-2xl shadow-inner p-2">
          {items.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              No items in your list. Add some items to proceed.
            </div>
          ) : (
            <div className="space-y-2">
              {items.map((item) => (
                <div key={item.id} className="flex flex-col border border-gray-100 p-2 bg-white rounded-xl">
                  <div className="flex items-center gap-3 p-2 animate-fade-in">
                    <div className="w-10 h-10 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-contain p-1"
                      />
                    </div>
                    <div className="flex-1 min-w-0 ">
                      <p className="font-medium truncate text-sm">
                        {item.name}
                      </p>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => onQuantityChange(item.id, item.quantity - 1, item.avalableQuantity, item.totalAddedQuantity)}
                          disabled={item.quantity <= 1}
                          className="w-7 h-7  border-gray-200 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed  "
                        >
                          <MinusCircleIcon className="hover:bg[#E2E3E5]"/>
                        </button>
                        <span className="w-6 text-center text-base font-bold text-pickTBlue">{item.quantity}</span>
                        <button
                          onClick={() => onQuantityChange(item.id, item.quantity + 1, item.avalableQuantity, item.totalAddedQuantity)}
                          className="w-7 h-7  border-gray-200 text-gray-600  "
                        >
                          <PlusCircleIcon className="" />
                        </button>
                      </div>
                    </div>

                    <button
                      onClick={() => onRemoveItem(item.id)}
                      className="w-8 h-8 text-gray-500 hover:text-red-500"
                    >
                       <DeleteIcon />
                    </button>
                  </div>
                  <div>
                    {item.unitType === "pack" && (
                      <div className="flex items-center gap-2 mt-2 px-5">
                        <button
                          onClick={() => onQuantityChange(item.id, item.quantity, item.avalableQuantity, item.totalAddedQuantity, "pack")}
                          className={`px-2 py-0 w-1/2 ${item.turnedTo === "pack" ? "bg-gray-200" : ""} border rounded-lg`}
                        >
                          Pack
                        </button>
                        <button
                          onClick={() => onQuantityChange(item.id, item.quantity, item.avalableQuantity, item.totalAddedQuantity, "individual")}
                          className={`px-2 py-0 w-1/2 ${item.turnedTo === "individual" ? "bg-gray-200" : ""} border rounded-lg`}
                        >
                          Individual
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex gap-3 font-lexend text-sm">
          <button
            onClick={onCancel}
            className="w-full text-[#0E0E0E] bg-[#F6F7F9] hover:bg-gray-200 normal-case py-2 rounded border border-[#0E0E0E]"
          >
            Cancel
          </button>
          <button
            onClick={() => setConfirmModalOpen(true)}
            disabled={items.length === 0}
            className="w-full bg-pickTBlue hover:bg-blue-600 normal-case text-white py-2 rounded disabled:opacity-50"
          >
            {type === "pick" ? "Pick" : "Drop"}
          </button>
        </div>

        {/* Confirmation Modal */}
        <Modal
          open={confirmModalOpen}
          onClose={() => setConfirmModalOpen(false)}
          aria-labelledby="confirmation-modal"
        >
          <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[90%] max-w-md flex flex-col gap-4">
            <h3 className="text-lg font-semibold">Confirm {type === "pick" ? "Pick" : "Drop"}</h3>
            <p>Are you sure you want to {type === "pick" ? "pick" : "drop"} these items?</p>
            <div className="flex justify-end gap-3 mt-2">
              <button 
                onClick={() => setConfirmModalOpen(false)}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
              >
                Cancel
              </button>
              <button 
                onClick={handleConfirm}
                className="px-4 py-2 text-sm bg-pickTBlue text-white rounded-md hover:bg-blue-600"
              >
                Confirm
              </button>
            </div>
          </Box>
        </Modal>
      </div>
    </div>
  );
};

export default PickList;
