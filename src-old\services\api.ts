import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// We'll set up the auth token in the component level using the useAuth hook
// This allows for more reactive token management

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    // Handle common errors (401, 403, etc.)
    if (error.response && error.response.status === 401) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('permissions');
      localStorage.removeItem('temp_token');
      localStorage.removeItem('userId');
      
      // Redirect to login page
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const pickService = {
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  }
};

const dropService = {
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  }
};

const supplierService = {
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  },
  get: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.get(url, config);
  },
  put: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.put(url, data, config);
  }
};

const consumableService = {
  get: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.get(url, config);
  },
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  },
  delete: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.delete(url, config);
  },
  put: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.put(url, data, config);
  }
};

const loginService = {
  post: (url: string, data = {}, config: AxiosRequestConfig = {}) => {
    return apiClient.post(url, data, config);
  }
};

const fileUploadService = {
  post: (url: string, data: File, token: string | null = null, config: AxiosRequestConfig = {}) => {
    const formData = new FormData();
    formData.append('file', data);
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      };
    } else {
      config.headers = {
        ...config.headers,
        'Content-Type': 'multipart/form-data'
      };
    }
    return apiClient.post(url, formData, config);
  }
};

const transactionService = {
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  }
};

const userService = {
  get: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.get(url, config);
  },
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  },
  put: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.put(url, data, config);
  },
  delete: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.delete(url, config);
  }
};

const projectService = {
  get: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.get(url, config);
  },
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  },
  put: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.put(url, data, config);
  },
  delete: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.delete(url, config);
  }
};

const resourceService = {
  get: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.get(url, config);
  },
  post: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.post(url, data, config);
  },
  put: (url: string, data = {}, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.put(url, data, config);
  },
  delete: (url: string, token: string | null = null, config: AxiosRequestConfig = {}) => {
    if (token) {
      config.headers = { ...config.headers, Authorization: `Bearer ${token}` };
    }
    return apiClient.delete(url, config);
  }
};

export {
  resourceService,
  userService,
  transactionService,
  fileUploadService,
  pickService,
  dropService,
  supplierService,
  consumableService,
  loginService,
  projectService
};
