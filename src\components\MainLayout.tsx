import AppSidebar from "./AppSidebar";
import Header from "./Header";
import { Outlet, useLocation } from "react-router-dom";
import { getPageTitle } from "../config/headingConfig";

const MainLayout = () => {
  const location = useLocation();
  const pageTitle = getPageTitle(location.pathname);

  return (
    <div className="flex h-screen w-full">
      <aside className="flex-shrink-0">
        <AppSidebar />
      </aside>
      <main className="flex-1 flex flex-col overflow-hidden">
        <Header title={pageTitle} />
        <div className="flex-1 overflow-y-auto">
          <Outlet />
        </div>
      </main>
    </div>
  );
};

export default MainLayout;
