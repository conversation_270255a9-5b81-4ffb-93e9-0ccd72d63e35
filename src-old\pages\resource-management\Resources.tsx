import React, { useState, useEffect } from 'react'
import Header from '../../components/general/Header'
import { resourceService } from '../../services/api'
import { useAuth } from '../../context/AuthContextProvider'
import { useNavigate } from 'react-router-dom'

// Define the Resource interface
interface Resource {
  id: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  totalAvailableQuantity: number;
  averagePricePerUnit: number;
  minInventoryQty: number;
  maxInventoryQty: number;
  resourceType: string;
}

const Resources = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { token } = useAuth();
  // Resources state
  const [resources, setResources] = useState<Resource[]>([]);

  const fetchResources = async () => {
    setLoading(true);
    try {
      const response = await resourceService.get('/resources/inventory', token);
      if (response.data) {
        setResources(response.data);
      }
      setError('');
    } catch (err) {
      console.error('Error fetching resources:', err);
      setError('Failed to load resources');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchResources();
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />

      <div className="w-full mx-auto px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold text-gray-800">Resource Management</h1>          <button
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            onClick={() => navigate('/consumables/add')}
          >
            Add New Resource
          </button>
        </div>
        {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="overflow-x-auto max-h-[calc(100vh-12rem)] overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center p-6">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (<table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resource Code</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Qty</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Unit</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">                {resources.map((resource) => (
                <tr key={resource?.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex-shrink-0 h-10 w-10">
                      <img
                        src={resource?.imageUrl}
                        alt={resource?.name}
                        className="h-10 w-10 rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://via.placeholder.com/40?text=No+Image';
                        }}
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{resource?.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{resource?.resourceCode}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{resource?.resourceCategory}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {resource?.unitType}
                    {resource?.unitSize > 1 && ` (${resource?.unitSize})`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{resource?.totalAvailableQuantity}</td>                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${resource?.averagePricePerUnit}</td>                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">                      <button
                    onClick={() => navigate(`/consumables/add?id=${resource.id}`)}
                    className="text-indigo-600 hover:text-indigo-900 mr-3"
                  >
                    Edit
                  </button><button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete this resource?')) {
                        resourceService.delete(`/resources/inventory/${resource.id}`, token)
                          .then(() => {
                            fetchResources();
                          })
                          .catch(err => {
                            console.error('Error deleting resource:', err);
                            setError('Failed to delete resource');
                          });
                      }
                    }}
                    className="text-red-600 hover:text-red-900"
                  >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
              </tbody>
            </table>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Resources