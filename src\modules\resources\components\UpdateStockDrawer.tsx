import { useState } from "react";
import { Package, X, Calendar } from "lucide-react";
import {
  <PERSON>et,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>itle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Resource, StockUpdateData } from "@/types/resource";

interface UpdateStockDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resource: Resource | null;
  onUpdateStock?: (resourceId: string, stockData: StockUpdateData) => void;
}

interface ReceiptHistoryItem {
  id: string;
  date: string;
  supplier: string;
  unitPrice: number;
  totalPrice: number;
  quantity: number;
}

const UpdateStockDrawer = ({ open, onOpenChange, resource, onUpdateStock }: UpdateStockDrawerProps) => {
  const [formData, setFormData] = useState<StockUpdateData>({
    date: new Date().toISOString().split('T')[0], // Today's date
    location: "",
    supplierName: "",
    supplierCode: "",
    supplierResourceCode: "",
    unitPrice: 0,
    quantity: 0,
  });

  // Mock receipt history data (in a real app, this would come from an API)
  const receiptHistory: ReceiptHistoryItem[] = [
    {
      id: "002",
      date: "July 18, 2025",
      supplier: "Rudd Industrial",
      unitPrice: 3.89,
      totalPrice: 47.68,
      quantity: 12,
    },
    {
      id: "001",
      date: "July 18, 2025",
      supplier: "Rudd Industrial",
      unitPrice: 3.99,
      totalPrice: 446.88,
      quantity: 112,
    },
  ];

  const handleInputChange = (field: keyof StockUpdateData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (resource && onUpdateStock) {
      onUpdateStock(resource.id, formData);
    }
    
    // Reset form
    setFormData({
      date: new Date().toISOString().split('T')[0],
      location: "",
      supplierName: "",
      supplierCode: "",
      supplierResourceCode: "",
      unitPrice: 0,
      quantity: 0,
    });
    onOpenChange(false);
  };

  const isFormValid = formData.date && formData.location && formData.supplierName && formData.quantity > 0 && formData.unitPrice > 0;

  if (!resource) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-[60vw] p-10 [&>button]:hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-0">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="w-fit p-2 bg-gray-100"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            
            {/* Title with margin */}
            <div className="mt-10">
              <SheetTitle className="text-xl font-semibold text-gray-900">
                Update Stock
              </SheetTitle>
              <SheetDescription className="text-sm text-gray-600 ">
                Restock items to keep your inventory accurate.
              </SheetDescription>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1  pb-6 overflow-y-auto mt-14">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Resource Details & Form */}
              <div className="space-y-6">
                {/* Resource Details */}
                <div className="space-y-2">
                  <h3 className="text-xl font-medium text-gray-900">Resource details</h3>
                  <div className="bg-primary-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-16 w-16 rounded border flex items-center justify-center bg-white">
                        {resource.imageUrl ? (
                          <img
                            src={resource.imageUrl}
                            alt={resource.name}
                            className="h-full w-full object-cover rounded"
                          />
                        ) : (
                          <Package className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{resource.name}</h4>
                        <p className="text-sm text-gray-500">Available: {resource.quantity || 0}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6 px-1">
                  {/* Date */}
                  <div className="space-y-2">
                    <Label htmlFor="date" className="text-sm font-medium text-gray-700">
                      Date
                    </Label>
                    <div className="relative">
                      <Input
                        id="date"
                        type="date"
                        value={formData.date}
                        onChange={(e) => handleInputChange('date', e.target.value)}
                        className="w-full h-11 pr-10 [&::-webkit-calendar-picker-indicator]:opacity-0 [&::-webkit-calendar-picker-indicator]:absolute [&::-webkit-calendar-picker-indicator]:right-0 [&::-webkit-calendar-picker-indicator]:w-10 [&::-webkit-calendar-picker-indicator]:h-full [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                      />
                      <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary-500 pointer-events-none" />
                    </div>
                  </div>

                  {/* Location */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Location
                    </Label>
                    <Select
                      value={formData.location}
                      onValueChange={(value) => handleInputChange('location', value)}
                    >
                      <SelectTrigger className="w-full !h-11">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="warehouse-a">Warehouse A</SelectItem>
                        <SelectItem value="warehouse-b">Warehouse B</SelectItem>
                        <SelectItem value="main-office">Main Office</SelectItem>
                        <SelectItem value="workshop">Workshop</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Supplier Details */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-semibold text-grey-900 uppercase tracking-wide">
                        SUPPLIER DETAILS
                      </h4>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="supplierName" className="text-sm font-medium text-gray-700">
                        Supplier Name
                      </Label>
                      <Select
                        value={formData.supplierName}
                        onValueChange={(value) => handleInputChange('supplierName', value)}
                      >
                        <SelectTrigger className="w-full !h-11">
                          <SelectValue placeholder="Select Supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Rudd Industrial">Rudd Industrial</SelectItem>
                          <SelectItem value="ABC Supplies">ABC Supplies</SelectItem>
                          <SelectItem value="XYZ Materials">XYZ Materials</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="supplierCode" className="text-sm font-medium text-gray-700">
                        Supplier Code
                      </Label>
                      <Input
                        id="supplierCode"
                        placeholder="Auto filled on selecting supplier"
                        value={formData.supplierCode}
                        onChange={(e) => handleInputChange('supplierCode', e.target.value)}
                        className="w-full h-11"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="supplierResourceCode" className="text-sm font-medium text-gray-700">
                        Supplier resource code
                      </Label>
                      <Input
                        id="supplierResourceCode"
                        placeholder="Enter supplier resource code"
                        value={formData.supplierResourceCode}
                        onChange={(e) => handleInputChange('supplierResourceCode', e.target.value)}
                        className="w-full h-11"
                      />
                    </div>
                  </div>

                  {/* Unit Price and Quantity */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="unitPrice" className="text-sm font-medium text-gray-700">
                        Unit Price
                      </Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                        <Input
                          id="unitPrice"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.unitPrice}
                          onChange={(e) => handleInputChange('unitPrice', parseFloat(e.target.value) || 0)}
                          className="w-full pl-8 h-11"
                          placeholder="0.00"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="quantity" className="text-sm font-medium text-gray-700">
                        Quantity
                      </Label>
                      <Input
                        id="quantity"
                        type="number"
                        min="1"
                        value={formData.quantity}
                        onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                        className="w-full h-11"
                        placeholder="Enter quantity"
                      />
                    </div>
                  </div>

                  {/* Total Cost */}
                  <div className="bg-yellow-100 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium text-gray-900">Total Cost</span>
                      <span className="text-xl font-bold text-gray-900">
                        $ {(formData.unitPrice * formData.quantity).toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="pt-4 grid grid-cols-2 gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      className="py-3 px-8"
                      onClick={() => onOpenChange(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-cyan-500 hover:bg-cyan-600 text-white py-3 px-8"
                      disabled={!isFormValid}
                    >
                      Add stock
                    </Button>
                  </div>
                </form>
              </div>

              {/* Right Column - Receipt History */}
              <div className="space-y-4 bg-primary-50 h-fit p-6 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Receipt History</h3>
                <div className="space-y-3">
                  {receiptHistory.map((receipt) => (
                    <div key={receipt.id} className="bg-white  rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm text-gray-900">Receipt #{receipt.id}</h4>
                        <span className="text-green-600 font-medium text-sm">+{receipt.quantity} Units</span>
                      </div>
                      {receipt.date && (
                        <p className="text-xs text-gray-500 mb-2">{receipt.date}</p>
                      )}
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Supplier:</span>
                          <span className="text-gray-900">{receipt.supplier}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Unit Price:</span>
                          <span className="text-gray-900">${receipt.unitPrice.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm font-medium">
                          <span className="text-gray-600">Total Price:</span>
                          <span className="text-gray-900">${receipt.totalPrice.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default UpdateStockDrawer;
