import React, { ReactElement } from 'react'
import { useAuth } from '../context/AuthContextProvider'
import { Navigate } from 'react-router-dom'

const ProtectedRouteForWorker = ({ children }: { children: React.ReactNode }): ReactElement => {
  const { user, loading } = useAuth()
  
  // Don't redirect while authentication is being checked
  if (loading) {
    return <div>Loading...</div> // Or a proper loading component
  }
  
  if (!user) {
    return <Navigate to={'/login'} />
  }
    return (user.roles.includes('supervisor') || user.roles.includes('mechanic') || user.roles.includes('admin')) ? (
    <>{children}</>
  ) : (
    <Navigate to={'/login'} />
  )
}

export default ProtectedRouteForWorker
