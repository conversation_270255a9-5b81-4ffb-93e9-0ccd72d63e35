import React from 'react';
import {
    Card,
    CardContent,
    Typography,
    Box,
    TextField,
    Button,
    Grid,
} from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface WeeklySummaryProps {
    data: {
        weekNumber: number;
        startDate: string;
        endDate: string;
        totalCost: number;
    }[];
    startDate: string;
    endDate: string;
    dateError: string | null;
    onStartDateChange: (value: string) => void;
    onEndDateChange: (value: string) => void;
    onFilter: () => void;
    onClear: () => void;
    formatCurrency: (amount: number) => string;
    formatDate: (dateString: string) => string;
}

const WeeklySummary: React.FC<WeeklySummaryProps> = ({
    data,
    startDate,
    endDate,
    dateError,
    onStartDateChange,
    onEndDateChange,
    onFilter,
    onClear,
    formatCurrency,
    formatDate
}) => {
    return (
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 rounded-xl h-full flex flex-col">
            <CardContent className="p-4 h-full flex flex-col">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
                    <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <Typography variant="h6" className="text-white">
                                📅
                            </Typography>
                        </div>
                        <div>
                            <Typography variant="h6" className="font-bold text-gray-800">
                                Weekly Summaries
                            </Typography>
                            <Typography variant="body2" className="text-gray-500">
                                Cost breakdown by weeks
                            </Typography>
                        </div>
                    </div>

                   
                </div>                {data && data.length > 0 ? (
                    <div className="flex-grow flex flex-col">
                        {/* Chart below weekly summaries */}
                        <Box className="flex-grow w-full mx-auto" style={{ maxWidth: '1200px', minHeight: '200px' }}>
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                    data={data}
                                    margin={{
                                        top: 20,
                                        right: 30,
                                        left: 20,
                                        bottom: 5,
                                    }}
                                >
                                    <defs>
                                        <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.9} />
                                            <stop offset="95%" stopColor="#1d4ed8" stopOpacity={0.7} />
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e0e4e7" />
                                    <XAxis
                                        dataKey="weekNumber"
                                        stroke="#6b7280"
                                        fontSize={12}
                                        tickFormatter={(value) => `Week ${value}`}
                                    />                                    <YAxis
                                        stroke="#6b7280"
                                        fontSize={12}
                                        tickFormatter={(value) => value >= 1000 ? `$${(value / 1000).toFixed(0)}k` : `$${value}`}
                                    />
                                    <Tooltip
                                        formatter={(value: number) => [formatCurrency(value), 'Total Cost']}
                                        labelFormatter={(label) => `Week ${label}`}
                                        contentStyle={{
                                            backgroundColor: '#f8fafc',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    <Bar
                                        dataKey="totalCost"
                                        fill="url(#colorGradient)"
                                        radius={[4, 4, 0, 0]}
                                        barSize={60} // Controls the width of each bar
                                    />                                </BarChart>
                            </ResponsiveContainer>
                        </Box>
                    </div>
                ) : (
                    <Box className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                        <Typography variant="body1" color="textSecondary" className="mb-2">
                            📊 No weekly data available
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                            Weekly summaries will appear here once data is available
                        </Typography>
                    </Box>
                )}
            </CardContent>
        </Card>
    );
};

export default WeeklySummary;
