import React, { useState, useEffect } from "react";
import toast from 'react-hot-toast';
import { useAuth } from "../../context/AuthContextProvider";
import { userService } from "../../services/api";
import Header from "../general/Header";
import UserTable from "./UserTable";
import AddUserModal from "./AddUserModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import ResetPasswordModal from "./ResetPasswordModal";
import { User, UserFormData } from "../../models/User";

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [error, setError] = useState("");
  const { token } = useAuth();

  const [formData, setFormData] = useState<UserFormData>({
    employeeId: "",
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    roles: [],
    isActive: true,
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [resetPasswordModalOpen, setResetPasswordModalOpen] = useState(false);
  const [userToReset, setUserToReset] = useState<string | null>(null);
  const [resetPasswordResponse, setResetPasswordResponse] = useState<{
    password?: string;
    message?: string;
  } | null>(null);
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);

  const fetchUsers = async () => {
    try {
      const response = await userService.get("/users", token);
      setUsers(response.data);
    } catch (err) {
      console.error("Error fetching users:", err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const toggleRole = (role: string) => {
    setFormData((prev) => {
      if (prev.roles.includes(role)) {
        return { ...prev, roles: prev.roles.filter((r) => r !== role) };
      } else {
        return { ...prev, roles: [...prev.roles, role] };
      }
    });
  };

  const handleCreateNew = () => {
    setFormData({
      employeeId: "",
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      roles: [],
      isActive: true,
    });
    setIsModalOpen(true);
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const handleError = (message: string) => {
    setError(message);
    setTimeout(() => {
      setError("");
    }, 3000);
  };

  const openDeleteModal = (userId: string) => {
    setUserToDelete(userId);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setUserToDelete(null);
  };

  const openResetPasswordModal = (userId: string) => {
    setUserToReset(userId);
    setResetPasswordModalOpen(true);
  };

  const closeResetPasswordModal = () => {
    setResetPasswordModalOpen(false);
    setUserToReset(null);
    setResetPasswordResponse(null);
  };

  const handleResetPassword = async () => {
    if (!userToReset) return;

    try {
      const response = await userService.post(
        `/auth/resetpassword`,
        { userId: userToReset },
        token
      );
      
      let password = '';
      let message = '';
      
      if (response.data) {
        password = response.data.temporaryPassword || '';
        message = response.data.message || '';
      }
      
      setResetPasswordResponse({ password, message });
      
      if (!password) {
        handleError("Temporary password not found in response. Please try again.");
      }
    } catch (err: any) {
      console.error("Error resetting password:", err);
      handleError("Something went wrong");
      closeResetPasswordModal();
    }
  };

  const handleDelete = async () => {
    if (!userToDelete) return;

    try {
      await userService.delete(`/users/${userToDelete}`, token);
      fetchUsers();
      closeDeleteModal();
    } catch (err: any) {
      console.error("Error deleting user:", err);
      handleError("Something went wrong");
    }
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (!formData.employeeId.trim()) {
        toast.error("Employee ID is required");
        return;
      }
      if (!formData.email.trim()) {
        toast.error("Email is required");
        return;
      }
      if (!formData.password.trim()) {
        toast.error("Password is required");
        return;
      }
      if (!formData.firstName.trim()) {
        toast.error("First name is required");
        return;
      }
      if (!formData.lastName.trim()) {
        toast.error("Last name is required");
        return;
      }
      if (formData.roles.length === 0) {
        toast.error("At least one role must be selected");
        return;
      }

      // Create user
      await userService.post("/auth/register", formData, token);

      fetchUsers();
      handleClose();
    } catch (err: any) {
      console.error("Error creating user:", err);
      toast.error("Something went wrong");
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <main>
      <Header />
      <div className="mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">User Management</h1>
          <div className="flex items-center">
            <input
              type="text"
              placeholder="Search by Employee ID or Name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 mr-4"
            />
            <button
              onClick={handleCreateNew}
              className="bg-pickTBlue hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
            >
              Add User
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* User Table Component */}
        <UserTable
          users={users.filter(
            (user) =>
              user.employeeId
                .toLowerCase()
                .includes(searchQuery.toLowerCase()) ||
              user.firstName
                .toLowerCase()
                .includes(searchQuery.toLowerCase()) ||
              user.lastName.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          openDeleteModal={openDeleteModal}
          openResetPasswordModal={openResetPasswordModal}
        />

        {/* Add User Modal Component */}
        <AddUserModal
          isOpen={isModalOpen}
          onClose={handleClose}
          formData={formData}
          handleInputChange={handleInputChange}
          toggleRole={toggleRole}
          handleSave={handleSave}
        />

        {/* Delete Confirmation Modal Component */}
        <DeleteConfirmationModal
          open={deleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={handleDelete}
        />        {/* Reset Password Modal Component */}
        <ResetPasswordModal
          open={resetPasswordModalOpen}
          onClose={closeResetPasswordModal}
          onReset={handleResetPassword}
          resetPasswordResponse={resetPasswordResponse}
          onConfirmClose={() => setShowCloseConfirmation(true)}
          showCloseConfirmation={showCloseConfirmation}
          onCancelConfirmation={() => setShowCloseConfirmation(false)}
          onConfirmAndClose={() => {
            setShowCloseConfirmation(false);
            closeResetPasswordModal();
          }}
        />
      </div>
    </main>
  );
};

export default UserManagement;
