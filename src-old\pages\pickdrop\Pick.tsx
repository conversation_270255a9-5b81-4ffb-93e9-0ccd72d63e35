import React, { useEffect, useState } from 'react'
import Header from '../../components/general/Header'
import SearchBar from '../../components/PickPage/SearchBar';
import BarcodeScanner from '../../components/PickPage/BarcodeScanner';
import InventoryTable from '../../components/PickPage/InventoryTable';
import EmployeeInfo from '../../components/PickPage/EmployeeInfo';
import PickList from '../../components/PickPage/PickList';
import axios from 'axios';
import { Close } from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContextProvider';
import { dropService, pickService, userService } from '../../services/api';
import DropTable, { ITransaction } from '../../components/PickPage/DropTable';
import { toast } from 'react-hot-toast';

// Define types

interface IConsumableInventory {
    id: string;
    name: string;
    resourceCode: string;
    unitType: string;
    unitSize: number;
    imageUrl: string;
    resourceCategory: string;
    totalAvailableQuantity: number;
    totalAddedQuantity: number;
    averagePricePerUnit: number;
}

type CartItem = {
    id: string;
    name: string;
    image: string;
    unitType: string;
    turnedTo: string;
    quantity: number;
    avalableQuantity: number;
    totalAddedQuantity: number;
};

interface IUser {
    id: string;
    employeeId: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    roles: string[];
    isActive: boolean;
    createdAt: string; // ISO date string
    updatedAt: string; // ISO date string
}

const Pick = () => {

    const [cartItems, setCartItems] = useState<CartItem[]>([]);
    const [type, setType] = useState<"pick" | "drop" | undefined>()
    const [searchWord, setSearchWord] = useState("")
    const [trigger, setTrigger] = useState(0)
    const [userId, setUserId] = useState<string | null>("")
    const { method } = useParams();
    const [error, setError] = useState<string | null>(null);
    const [users, setUsers] = useState<IUser[]>([])
    const [selectedUser, setSelectedUser] = useState<IUser | null>(null)
    const [projectId, setProjectId] = useState<string | null>("")

    const { user, token } = useAuth()

    // const fetchEmployee = async () => {
    //     const userId = localStorage.getItem('employeeid');
    //     setUserId(userId)
    // }

    useEffect(() => {
        if (method === "pick") {
            setType("pick")
        } else if (method === "drop") {
            setType("drop")
        }
    }, [method])




    // Handle adding items to cart
    const handleAddToCart = (product: IConsumableInventory) => {
        setCartItems((prev) => {
            // Check if item already exists in cart
            const existingItemIndex = prev.findIndex((item) => item.id === product.id);
            // Check if adding more would exceed the total available quantity
            if (type === "pick") {
                const currentQuantity = existingItemIndex >= 0 ? prev[existingItemIndex].quantity : 0;
                if (currentQuantity + 1 > product.totalAvailableQuantity) {
                    // ...existing code...
                    return prev; // Return the previous state without changes
                }
            }
            if (type === "drop") {
                const currentQuantity = existingItemIndex >= 0 ? prev[existingItemIndex].quantity : 0;
                const maxDropQuantity = product.totalAddedQuantity - product.totalAvailableQuantity;
                if (currentQuantity + 1 > maxDropQuantity) {
                    // ...existing code...
                    return prev; // Return the previous state without changes
                }
            }
            if (existingItemIndex >= 0) {
                // If item exists, create a new array with updated quantity
                const newItems = [...prev];
                newItems[existingItemIndex] = {
                    ...newItems[existingItemIndex],
                    quantity: newItems[existingItemIndex].quantity + 1,
                };
                return newItems;
            } else {
                // If item doesn't exist, add it to cart
                const newItem: CartItem = {
                    id: product.id,
                    name: product.name,
                    image: product.imageUrl,
                    unitType: product.unitType,
                    turnedTo: product.unitType,
                    quantity: 1,
                    avalableQuantity: product.totalAvailableQuantity,
                    totalAddedQuantity: product.totalAddedQuantity
                };

                return [...prev, newItem];
            }
        });
    };


    // Handle quantity change
    const handleQuantityChange = (id: string, quantity: number, availableQuantity: number, totalAddedQuantity:number, unitType?: string) => {
        
        if (unitType) {
            setCartItems((prev) =>
                prev.map((item) => (item.id === id ? { ...item, turnedTo: unitType } : item))
            );
        }
        if (quantity <= 0) return;
        if ((quantity > availableQuantity) && type === "pick") {
            // ...existing code...
            return;
        }
        if (type === "drop") {
            const maxDropQuantity = totalAddedQuantity - availableQuantity;
            if (quantity > maxDropQuantity) {
                // ...existing code...
                return;
            }
        }
        setCartItems((prev) =>
            prev.map((item) => (item.id === id ? { ...item, quantity } : item))
        );
    };

    // Handle removing item from cart
    const handleRemoveItem = (id: string) => {
        setCartItems((prev) => {
            const itemToRemove = prev.find(item => item.id === id);
            return prev.filter((item) => item.id !== id);
        });
    };


    const handleDrop = async (drop: ITransaction) => {
        // ...existing code...
        const dropObject = {
            items: [
                {
                    resourceId: drop.resourceId,
                    quantity: drop.effectiveQuantity,
                    unitType: drop.unitType
                }
            ],
            userId: drop.userId,
            projectId: drop.project?.id,
            pickId: drop.id
        }

        try {
            const response = await dropService.post('/resources/inventory/drop', dropObject, token);
            if (response.status === 201) {
                // ...existing code...
                setTrigger(trigger + 1);
                setCartItems([]);
                toast.success(`Item dropped successfully!`);
            }
        } catch (error: any) {
            console.error("Error sending drop request", error);
            if (error?.response?.data?.message) {
                handleError(error.response.data.message);
                toast.error(error.response.data.message);
            } else {
                handleError("Failed to drop item");
                toast.error("Failed to drop item");
            }
        }
        return;

    }

    // Handle pick all
    const handlePickAll = async () => {
        if (cartItems.length === 0) {
            return;
        }
        // ...existing code...
        const userId = selectedUser?.id 
        if (!userId) {
            handleError("User ID is required");
            return;
        }
        if (!projectId) {
            handleError("Project ID is required");
            return;
        }
        const pickRequest = {
            items: cartItems.map((item) => ({
                resourceId: item.id,
                quantity: item.quantity,
                unitType: item.unitType === item.turnedTo ? item.unitType : item.turnedTo,
            })),
            userId: userId,
            projectId: projectId,
        };
        if (type === "drop") {
            try {
                const response = await dropService.post('/resources/inventory/drop', pickRequest, token);
                if (response.status === 201) {
                    // ...existing code...
                    setTrigger(trigger + 1);
                    setCartItems([]);
                    toast.success(`Items dropped successfully!`);
                }
            } catch (error: any) {
                console.error("Error sending pick request");
                console.error(error)
                if (error?.response && error.response.data && error.response.data.message) {
                    if (error.response.data.message === "Cannot drop more quantity than the total added quantity across all batches") {
                        handleError("Invalid quantity")
                    }
                }
            }
        }
        else {
            try {
                const response = await pickService.post('/resources/inventory/pick', pickRequest, token);
                if (response.status === 201) {
                    // ...existing code...
                    setTrigger(trigger + 1);
                    setCartItems([]);
                    setSelectedUser(null);
                    setProjectId(null);
                    toast.success(`Items picked successfully!`);
                }
            } catch (error) {
                console.error("Error sending pick request");
                console.error(error)
            }
        }
    };

    const handleError = (message: string) => {
        setError(message)
        setTimeout(() => {
            setError('')
        }, 3000)
    }

    const handlePickOrDrop = (value: "pick" | "drop") => {
        setType(value)
    }

    // Handle cancel
    const handleCancel = () => {
        if (cartItems.length > 0) {
            setCartItems([]);
        }
    };

    const fetchUser = async () => {
        const user = await userService.get("/users", token)
        if (user.status === 200) {
            setUsers(user.data)
        }
        // ...existing code...
    }

    useEffect(() => {
        // fetchEmployee()
        fetchUser()
    }, [])


    return (
        <div className='min-h-screen'>
            <Header />
            {type === undefined && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 ">
                    <div className="flex flex-col items-center bg-mainbg p-4 sm:p-8 w-11/12 sm:w-4/5 md:w-3/5 lg:w-2/5 font-lexend rounded-xl relative" >
                        <button
                            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                            onClick={() => window.location.href = '/'}
                        >
                            <Close />
                        </button>
                        <h2 className="text-xl sm:text-2xl font-bold mb-4">Select Operation</h2>
                        <div className="flex justify-center gap-3 sm:gap-5">
                            <button
                                className="bg-pickTBlue hover:bg-blue-700 font-semibold text-sm sm:text-base text-white py-2 px-6 sm:px-10 rounded"
                                onClick={() => handlePickOrDrop("pick")}
                            >
                                Pick
                            </button>
                            <button
                                className="bg-pickTBlue hover:bg-blue-700 font-semibold text-sm sm:text-base text-white py-2 px-6 sm:px-10 rounded"
                                onClick={() => handlePickOrDrop("drop")}
                            >
                                Drop
                            </button>
                        </div>
                    </div>
                </div>
            )}
            <main className='flex  h-[calc(100vh-3.5rem)] px-4 pt-2 bg-[#F2F4F7]'>
                <div className={`space-y-4 h-full w-full ${type === "drop" ? "w-full" : "lg:w-2/3"}`}>
                    <div className="flex flex-col sm:flex-row gap-3 items-center ">
                        <div className="w-full sm:flex-1">
                            <SearchBar onChange={(e) => setSearchWord(e.target.value)} />
                        </div>
                        <BarcodeScanner />
                    </div>
                    {type === "drop" ? (
                        <DropTable onDropItem={handleDrop}  searchWord={searchWord} key={trigger} />
                    ) : (
                        <InventoryTable onAddToCart={handleAddToCart} searchWord={searchWord} type={type} key={trigger} />
                    )}
                </div>

                {type !== "drop" && (
                    <div className="w-full lg:w-1/3 pl-4 ">
                        <div className='flex flex-col p-3 gap-3 border h-[calc(100vh-4.7rem)] rounded-lg bg-white '>
                            <EmployeeInfo type={type} users={users} projectId={projectId} selectedUser={selectedUser} setSelectedUser={setSelectedUser} handlePojectId={setProjectId} />
                            <PickList
                                error={error}
                                items={cartItems}
                                onQuantityChange={handleQuantityChange}
                                onRemoveItem={handleRemoveItem}
                                onPickAll={handlePickAll}
                                onCancel={handleCancel}
                                type={type}
                            />
                        </div>
                    </div>
                )}
            </main>


            {/* <div className="overflow-hidden flex flex-col bg-gray-50">
                {type === undefined && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 ">
                        <div className="flex flex-col items-center bg-mainbg p-4 sm:p-8 w-11/12 sm:w-4/5 md:w-3/5 lg:w-2/5 font-lexend rounded-xl relative" >
                            <button
                                className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                                onClick={() => window.location.href = '/'}
                            >
                                <Close />
                            </button>
                            <h2 className="text-xl sm:text-2xl font-bold mb-4">Select Operation</h2>
                            <div className="flex justify-center gap-3 sm:gap-5">
                                <button
                                    className="bg-pickTBlue hover:bg-blue-700 font-semibold text-sm sm:text-base text-white py-2 px-6 sm:px-10 rounded"
                                    onClick={() => handlePickOrDrop("pick")}
                                >
                                    Pick
                                </button>
                                <button
                                    className="bg-pickTBlue hover:bg-blue-700 font-semibold text-sm sm:text-base text-white py-2 px-6 sm:px-10 rounded"
                                    onClick={() => handlePickOrDrop("drop")}
                                >
                                    Drop
                                </button>
                            </div>
                        </div>
                    </div>
                )}
                <div className='border border-red-500'>
                    <div className="m-2 sm:m-4 md:m-6 h-full font-nunito flex flex-col lg:flex-row border">
                        <div className="space-y-4 h-full w-full lg:w-2/3 mb-4 lg:mb-0">
                            <div className="flex flex-col sm:flex-row gap-3 items-center">
                                <div className="w-full sm:flex-1">
                                    <SearchBar onChange={(e) => setSearchWord(e.target.value)} />
                                </div>
                                <BarcodeScanner />
                            </div>
                            <InventoryTable onAddToCart={handleAddToCart} searchWord={searchWord} type={type} key={trigger} />
                        </div>
                        <div className=" space-y-4 w-full lg:w-1/3 lg:pl-4">
                            <div className='flex flex-col p-3 sm:p-5 border h-full rounded-lg'>
                                <EmployeeInfo name="Employee" id={userId ? userId : ""} />
                                <PickList
                                    items={cartItems}
                                    onQuantityChange={handleQuantityChange}
                                    onRemoveItem={handleRemoveItem}
                                    onPickAll={handlePickAll}
                                    onCancel={handleCancel}
                                    type={type}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div> */}
        </div>
    )
}

export default Pick
