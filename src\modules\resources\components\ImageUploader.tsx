import { useState, useRef } from "react";
import ReactCrop, { type Crop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload } from "lucide-react";
import { uploadFile } from "@/services/api";
import toast from "react-hot-toast";

interface ImageUploaderProps {
  onImageCropped: (croppedImage: string) => void;
  initialImage?: string;
}

const ImageUploader = ({ onImageCropped, initialImage }: ImageUploaderProps) => {
  const [imgSrc, setImgSrc] = useState("");
  const [uploadedImg, setUploadedImg] = useState<string | undefined>(initialImage);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [crop, setCrop] = useState<Crop>();
  const [zoom, setZoom] = useState(1);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const reader = new FileReader();
      reader.addEventListener("load", () => {
        setImgSrc(reader.result?.toString() || "");
        setCrop({
          unit: 'px',
          width: 200,
          height: 200,
          x: 0,
          y: 0,
        });
      });
      reader.readAsDataURL(e.target.files[0]);
    }
  };

  const handleCrop = async () => {
    if (imgRef.current && crop?.width && crop?.height) {
      const canvas = document.createElement("canvas");
      const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
      const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
      canvas.width = crop.width * scaleX;
      canvas.height = crop.height * scaleY;
      const ctx = canvas.getContext("2d");

      if (ctx) {
        ctx.drawImage(
          imgRef.current,
          crop.x * scaleX,
          crop.y * scaleY,
          crop.width * scaleX,
          crop.height * scaleY,
          0,
          0,
          crop.width * scaleX,
          crop.height * scaleY
        );
        canvas.toBlob(async (blob) => {
          if (blob) {
            try {
              const file = new File([blob], "cropped-image.png", {
                type: "image/png",
              });
              const response = await uploadFile(file);
              onImageCropped(response.url);
              setUploadedImg(response.url);
              setImgSrc("");
              toast.success("Image uploaded successfully");
            } catch (error) {
              console.error("Failed to upload image:", error);
              toast.error("Failed to upload image");
            }
          }
        }, "image/png");
      }
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium text-gray-900">
        Resource Image
      </Label>
      {!imgSrc && !uploadedImg && (
        <div
          className="border-2 border-dashed border-primary-500 rounded-lg p-8 text-center cursor-pointer"
          onClick={handleBrowseClick}
        >
          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 mb-2">Click to browse</p>
          <Button type="button" variant="default" size="sm" className="bg-gray-100 text-gray-600">
            Browse
          </Button>
        </div>
      )}
      {uploadedImg && !imgSrc && (
        <div className="border-2 border-dashed border-primary-500 rounded-lg p-8 flex items-center justify-center space-x-4">
          <img src={uploadedImg} alt="Cropped" className="w-[150px] h-[150px] rounded-lg object-cover" />
          <Button type="button" variant="outline" onClick={handleBrowseClick}>
            Change Image
          </Button>
        </div>
      )}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
      {imgSrc && (
        <div className="space-y-4">
          <div style={{ width: 200, height: 200 }}>
            <ReactCrop
              crop={crop}
              onChange={(c) => setCrop(c)}
              aspect={1}
              locked
            >
              <img
                ref={imgRef}
                src={imgSrc}
                style={{ transform: `scale(${zoom})`, objectFit: 'contain', width: 200, height: 200 }}
                alt="Crop me"
              />
            </ReactCrop>
          </div>
          <div className="flex items-center space-x-2">
            <Label htmlFor="zoom">Zoom:</Label>
            <input
              id="zoom"
              type="range"
              min="1"
              max="3"
              step="0.1"
              value={zoom}
              onChange={(e) => setZoom(Number(e.target.value))}
              className="w-full"
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setImgSrc("")}>
              Cancel
            </Button>
            <Button type="button" onClick={handleCrop}>
              Done
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
