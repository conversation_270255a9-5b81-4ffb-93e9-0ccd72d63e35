export interface IUser {
    firstName: string
    lastName: string
}

export interface User {
    id: string;
    employeeId: string;
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface UserFormData {
    employeeId: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    roles: string[];
    isActive?: boolean;
}