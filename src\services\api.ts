import axios from 'axios';
import type { AxiosInstance } from 'axios';
import type { ResourceFormData } from 'src/types/resource';
import type { UserFormData } from 'src/types/user';

// Base API configuration
const BASE_URL = '/api'; // Using proxy path
// const BASE_URL = 'http://localhost:4000/api'; 

// Create axios instance with base configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Resource endpoints
export const getResources = async () => {
  const response = await apiClient.get('/resources');
  return response.data;
};

export const createResource = async (resourceData: ResourceFormData) => {
  const response = await apiClient.post('/resources', resourceData);
  return response.data;
};

export const updateResource = async (resourceId: string, resourceData: ResourceFormData) => {
  const response = await apiClient.put(`/resources/${resourceId}`, resourceData);
  return response.data;
};

// User endpoints
export const getUsers = async () => {
  const response = await apiClient.get('/users');
  return response.data;
};

export const createUser = async (userData: UserFormData) => {
  const response = await apiClient.post('/users', userData);
  return response.data;
};

export const updateUser = async (userId: string, userData: Partial<UserFormData>) => {
  const response = await apiClient.put(`/users/${userId}`, userData);
  return response.data;
};

export const deleteUser = async (userId: string) => {
  const response = await apiClient.delete(`/users/${userId}`);
  return response.data;
};

// File upload endpoint
export const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await apiClient.post("/file-upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};
