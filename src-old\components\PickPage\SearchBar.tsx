import React from 'react';

const SearchBar = ({onChange}:{onChange:(e:React.ChangeEvent<HTMLInputElement>)=>void}) => {
  return (
    <div className="relative font-nunito">
      <div className="absolute z-10 inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg className="h-4 w-4 text-[#FE7BA0]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        placeholder="Search Consumables..."
        className="w-full py-2 pl-10 pr-3 rounded-[10px] text-sm font-normal border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        onChange={onChange}
      />
    </div>
  );
};

export default SearchBar;
