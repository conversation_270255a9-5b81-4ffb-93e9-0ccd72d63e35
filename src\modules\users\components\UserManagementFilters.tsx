import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter } from "lucide-react";
import { type User } from "@/types/user";

interface UserManagementFiltersProps {
  users: User[];
  onFilter: (filteredUsers: User[]) => void;
}

const UserManagementFilters = ({
  users,
  onFilter,
}: UserManagementFiltersProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("All Roles");
  const [selectedStatus, setSelectedStatus] = useState("All Status");

  useEffect(() => {
    const filterUsers = () => {
      const filtered = users.filter((user: User) => {
        const fullName = `${user.firstName} ${user.lastName}`;
        const matchesSearch =
          searchTerm === "" ||
          fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.employeeId?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesRole =
          selectedRole === "All Roles" ||
          selectedRole.toLowerCase() === user.role?.toLowerCase();

        const matchesStatus =
          selectedStatus === "All Status" ||
          (selectedStatus === "ACTIVE" && user.isActive) ||
          (selectedStatus === "INACTIVE" && !user.isActive);

        return matchesSearch && matchesRole && matchesStatus;
      });
      onFilter(filtered);
    };

    filterUsers();
  }, [users, searchTerm, selectedRole, selectedStatus, onFilter]);

  const hasActiveFilters =
    selectedRole !== "All Roles" || selectedStatus !== "All Status";

  return (
    <div className="flex items-center gap-4 mb-6 justify-between">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search name, email or ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 w-72"
        />
      </div>

      {/* Filter Button */}
      <div className="flex items-center gap-2">
        <div
          className={`flex items-center gap-2 px-3 py-2 border rounded-md ${
            hasActiveFilters
              ? "bg-blue-50 border-blue-200 text-blue-700"
              : "border-gray-200 text-gray-700"
          }`}
        >
          <Filter
            className={`h-4 w-4 ${
              hasActiveFilters ? "text-blue-500" : "text-gray-500"
            }`}
          />
          <span className="text-sm">Filter by</span>
        </div>

        <Select value={selectedRole} onValueChange={setSelectedRole}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="All Roles" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All Roles">All Roles</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="supervisor">Supervisor</SelectItem>
            <SelectItem value="mechanic">Mechanic</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All Status">All Status</SelectItem>
            <SelectItem value="ACTIVE">Active</SelectItem>
            <SelectItem value="INACTIVE">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default UserManagementFilters;
