import React, { useState } from 'react'
import Header from '../../components/general/Header'
import axios from 'axios'
import SearchSupplier from '../../components/supplier-management/SearchSupplier';
import SuppliersTable from '../../components/supplier-management/SuppliersTable';
import UpdateSupplierModal from '../../components/supplier-management/UpdateSupplierModal';
import { supplierService } from '../../services/api';
import { useAuth } from '../../context/AuthContextProvider';

interface Supplier {
    id: string;
    name: string;
    code: string;
    phoneNumber: string;
    contactPerson: string;
    contactEmail?: string;
    address?: string;
}

interface UpdateSupplier {
    name: string;
    code: string;
    phoneNumber: string;
    contactPerson: string;
    contactEmail: string;
    address: string;
}

const Suppliers = () => {
    const [suppliers, setSuppliers] = useState<Supplier[]>([])
    const [filtered, setFilterd] = useState<Supplier[]>([])
    const [searchTerm, setSearchTerm] = useState('')
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [selectedSupplier, setSelectedSupplier] = useState<UpdateSupplier>({
        name: '',
        code: '',
        phoneNumber: '',
        contactPerson: '',
        contactEmail: '',
        address: ''
    })
    const [currentSupplierId, setCurrentSupplierId] = useState<string>('')
    const [isCreating, setIsCreating] = useState(false)
    const [error, setError] = useState('')

    const { token } = useAuth()

    const fetchSuppliers = async () => {
        // const suppliers = await axios.get("/api/suppliers")
        const suppliers = await supplierService.get("/suppliers", token)
        // ...existing code...
        setSuppliers(suppliers.data)
        setFilterd(suppliers.data)
    }

    const filterSupplyData = (value: string) => {
        setSearchTerm(value)
        const filtered = suppliers.filter((sup) =>
            sup.name.toLowerCase().includes(value.toLowerCase()) ||
            sup.code.toLowerCase().includes(value.toLowerCase())
        )
        setFilterd(filtered)
    }

    const handleEdit = (supplier: Supplier) => {
        setIsCreating(false)
        setCurrentSupplierId(supplier.id)
        setSelectedSupplier({
            name: supplier.name,
            code: supplier.code,
            phoneNumber: supplier.phoneNumber,
            contactPerson: supplier.contactPerson,
            contactEmail: supplier.contactEmail || '',
            address: supplier.address || ''
        })
        setIsModalOpen(true)
    }

    const handleCreateNew = () => {
        setIsCreating(true)
        setCurrentSupplierId('')
        setSelectedSupplier({
            name: '',
            code: '',
            phoneNumber: '',
            contactPerson: '',
            contactEmail: '',
            address: ''
        })
        setIsModalOpen(true)
    }

    const handleClose = () => {
        setIsModalOpen(false)
        setSelectedSupplier({
            name: '',
            code: '',
            phoneNumber: '',
            contactPerson: '',
            contactEmail: '',
            address: ''
        })
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setSelectedSupplier(prev => ({
            ...prev,
            [name]: value
        }))
    }

    const handleError = (message: string) => {
        setError(message)
        setTimeout(() => {
            setError('')
        }, 3000)
    }

    const handleSave = async () => {
        try {
            const supplierData = {
                name: selectedSupplier.name,
                code: selectedSupplier.code,
                contactEmail: selectedSupplier.contactEmail,
                phoneNumber: selectedSupplier.phoneNumber,
                address: selectedSupplier.address,
                contactPerson: selectedSupplier.contactPerson
            }

            // Validate supplier name and code
            if (!supplierData.name.trim()) {
                handleError('Supplier name is required')
                return
            }
            if (!supplierData.code.trim()) {
                handleError('Supplier code is required')
                return
            }

            if (isCreating) {
                // await axios.post('/api/suppliers', supplierData)
                await supplierService.post('/suppliers', supplierData, token)
            } else {
                // await axios.put(`/api/suppliers/${currentSupplierId}`, supplierData)
                await supplierService.put(`/suppliers/${currentSupplierId}`, supplierData, token)
            }
            fetchSuppliers() // Refresh the list
            handleClose()
        } catch (error) {
            console.error('Error saving supplier:', error)
        }
    }

    React.useEffect(() => {
        fetchSuppliers()
    }, [])

    return (
        <div className='bg-mainbg min-h-screen'>
            <Header />
            <div className=" bg-white m-4 rounded-xl">
                <div className='px-4 py-3'>
                    <SearchSupplier
                        searchTerm={searchTerm}
                        onSearch={filterSupplyData}
                        onCreateNew={handleCreateNew}
                    />
                </div>

                <SuppliersTable
                    suppliers={filtered}
                    onEdit={handleEdit}
                />

                <UpdateSupplierModal
                    error={error}
                    open={isModalOpen}
                    onClose={handleClose}
                    onSave={handleSave}
                    supplier={selectedSupplier}
                    onInputChange={handleInputChange}
                    isCreating={isCreating}
                />
            </div>
        </div>
    )
}

export default Suppliers