"use client";

import { useState } from "react";
import type { Row } from "@tanstack/react-table";
import { Edit, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import UpdateStockDrawer from "@/modules/resources/components/UpdateStockDrawer";
import AddOrEditResourceDrawer from "./AddOrEditResourceDrawer";
import type { Resource } from "@/types/resource";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
  onResourceUpdate: () => void;
}

export function DataTableRowActions<TData>({
  row,
  onResourceUpdate,
}: DataTableRowActionsProps<TData>) {
  const [isUpdateStockDrawerOpen, setUpdateStockDrawerOpen] = useState(false);
  const [isEditDrawerOpen, setEditDrawerOpen] = useState(false);
  const resource = row.original as Resource;

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          className="bg-[#F4F4F4]"
          onClick={() => setEditDrawerOpen(true)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="link"
          size="icon"
          className="bg-success-600"
          onClick={() => setUpdateStockDrawerOpen(true)}
        >
          <Plus className="h-4 w-4" color="white" />
        </Button>
      </div>
      <UpdateStockDrawer
        open={isUpdateStockDrawerOpen}
        onOpenChange={setUpdateStockDrawerOpen}
        resource={resource}
      />
      <AddOrEditResourceDrawer
        open={isEditDrawerOpen}
        onOpenChange={setEditDrawerOpen}
        resource={resource}
        onResourceUpdate={onResourceUpdate}
      />
    </>
  );
}
