import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { ErrorState, LoadingState } from "@/components/common/PageStates";
import type { User, UserFormData } from "@/types/user";
import AddUserDrawer from "./components/AddUserDrawer";
import UserManagementHeader from "./components/UserManagementHeader";
import UserManagementFilters from "./components/UserManagementFilters";
import { DataTable } from "@/components/DataTable";
import { getColumns } from "./components/columns";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getUsers, createUser, updateUser } from "@/services/api";

const userKeys = {
  all: ["users"] as const,
  lists: () => [...userKeys.all, "list"] as const,
};

const UserManagementPage = () => {
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);

  const {
    data: users = [],
    isLoading,
    error,
  } = useQuery<User[]>({
    queryKey: userKeys.lists(),
    queryFn: getUsers,
  });

  const { mutateAsync: addUser, isPending: isCreating } = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });

  const { mutateAsync: updateUserData, isPending: isUpdating } = useMutation({
    mutationFn: ({
      userId,
      userData,
    }: {
      userId: string;
      userData: Partial<User>;
    }) => updateUser(userId, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });

  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    await updateUserData({ userId, userData: { isActive: !currentStatus } });
  };

  const resetUserPassword = async (userId: string) => {
    // In a real application, this would make an API call to reset the password
    console.log(`Password reset requested for user ${userId}`);
    // You could show a toast notification here
  };


  const columns = getColumns({
    toggleUserStatus,
    resetUserPassword,
  });


  const handleAddUser = async (userData: UserFormData) => {
    try {
      await addUser(userData);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Failed to add user:", error);
      // You could show a toast notification here
    }
  };


  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading users..." />;
  }

  // Error state
  if (error) {
    return <ErrorState error={error} />;
  }

  return (
    <div className="p-6 bg-white min-h-[90vh]">
      <UserManagementHeader
        setIsAddModalOpen={setIsAddModalOpen}
        isCreating={isCreating}
      />

      <UserManagementFilters users={users} onFilter={setFilteredUsers} />

      <DataTable columns={columns} data={filteredUsers} />

      {/* Add User Drawer */}
      <AddUserDrawer
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddUser}
      />
    </div>
  );
};

export default UserManagementPage;
