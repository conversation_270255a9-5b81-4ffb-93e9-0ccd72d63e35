export const CROP_CONFIG = {
    SIZE: { width: 250, height: 250 },
    ASPECT_RATIO: 4 / 3,
    CONTAINER: { 
        width: 320, // w-80 in Tailwind (80 * 4 = 320px)
        height: 240 // h-60 in Tailwind (60 * 4 = 240px)
    },
    ZOOM: { 
        min: 0.1, 
        max: 3, 
        step: 0.1,
        default: 0.3
    },
    CONTAINER_HEIGHT: 800
} as const;

export const FILE_CONFIG = {
    ACCEPT: "image/*",
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    QUALITY: 0.95,
    FORMAT: "image/jpeg",
    FILENAME: "cropped-image.jpg"
} as const;
