import { error } from 'console';
import React from 'react'

interface UpdateSupplier {
    name: string;
    code: string;
    phoneNumber: string;
    contactPerson: string;
    contactEmail: string;
    address: string;
}

interface UpdateSupplierModalProps {
    open: boolean;
    onClose: () => void;
    onSave: () => void;
    supplier: UpdateSupplier;
    onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    isCreating: boolean;
    error: string;
}

const UpdateSupplierModal: React.FC<UpdateSupplierModalProps> = ({
    open,
    onClose,
    onSave,
    supplier,
    onInputChange,
    isCreating,
    error
}) => {
    if (!open) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center font-nunito">
            <div className="bg-white rounded-lg shadow-xl p-6 w-[600px]">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">
                        {isCreating ? 'Add Supplier' : 'Update Supplier'}
                    </h2>
                    {
                        error && <p className="text-red-500 text-sm bg-red-200 px-3 rounded-full">{error}</p>
                    }
                    <button onClick={onClose} className="text-xl">&times;</button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="text-sm font-medium block mb-1">Supplier Name <sup className='text-red-500 '>*</sup></label>
                        <input
                            type="text"
                            name="name"
                            value={supplier.name}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Supplier Name"
                            required
                        />
                    </div>
                    <div>
                        <label className="text-sm font-medium block mb-1">Supplier Code <sup className='text-red-500'>*</sup></label>
                        <input
                            type="text"
                            name="code"
                            value={supplier.code}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Code"
                        />
                    </div>
                    <div>
                        <label className="text-sm font-medium block mb-1">Contact Email Id</label>
                        <input
                            type="email"
                            name="contactEmail"
                            value={supplier.contactEmail}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Contact Email Id"
                        />
                    </div>
                    <div>
                        <label className="text-sm font-medium block mb-1">Phone Number</label>
                        <input
                            type="tel"
                            name="phoneNumber"
                            value={supplier.phoneNumber}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Phone Number"
                        />
                    </div>
                    <div className="col-span-2">
                        <label className="text-sm font-medium block mb-1">Address</label>
                        <input
                            type="text"
                            name="address"
                            value={supplier.address}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Address"
                        />
                    </div>
                    <div className="col-span-2">
                        <label className="text-sm font-medium block mb-1">Contact Person</label>
                        <input
                            type="text"
                            name="contactPerson"
                            value={supplier.contactPerson}
                            onChange={onInputChange}
                            className="w-full p-2 border rounded text-sm font-medium"
                            placeholder="Enter Contact Person"
                        />
                    </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                    <button
                        onClick={onClose}
                        className=" text-sm font-medium bg-transparent border-black border text-black px-4 py-2 rounded font-lexend"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onSave}
                        className="text-white text-sm font-medium bg-pickTBlue px-4 py-2 rounded font-lexend"
                    >
                        {isCreating ? 'Create' : 'Save'}
                    </button>
                </div>
            </div>
        </div>
    )
}

export default UpdateSupplierModal

