import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  startIndex: number;
  endIndex: number;
  pageNumbers: (number | string)[];
  canGoPrevious: boolean;
  canGoNext: boolean;
  onPageChange: (page: number) => void;
  onPrevious: () => void;
  onNext: () => void;
  itemName?: string;
  showWhenFewItems?: boolean;
  variant?: 'table' | 'standalone';
  className?: string;
}

const Pagination = ({
  currentPage,
  totalPages,
  totalItems,
  startIndex,
  endIndex,
  pageNumbers,
  canGoPrevious,
  canGoNext,
  onPageChange,
  onPrevious,
  onNext,
  itemName = "items",
  showWhenFewItems = false,
  variant = 'table',
  className = ''
}: PaginationProps) => {
  // Only show pagination if there are multiple pages or showWhenFewItems is true
  if (!(totalPages > 1 || showWhenFewItems)) {
    return null;
  }

  const baseClasses = variant === 'table' 
    ? "border-t bg-white px-6 py-4 flex items-center justify-between rounded-b-xl"
    : "flex items-center justify-between mt-6";

  const textColorClass = variant === 'table' ? "text-slate-600" : "text-gray-700";

  return (
    <div className={`${baseClasses} ${className}`}>
      <div className={`text-sm ${textColorClass}`}>
        Showing {totalItems > 0 ? startIndex + 1 : 0}-{Math.min(endIndex, totalItems)} of {totalItems} {itemName}
      </div>
      <div className="flex items-center gap-1">
        <div className="flex items-center gap-1">
          {pageNumbers.map((page, index) => (
            <Button
              key={index}
              variant={page === currentPage ? "default" : "ghost"}
              size="sm"
              className={`h-8 min-w-[32px] ${
                page === currentPage
                  ? "bg-blue-500 text-white hover:bg-blue-600 border-blue-500"
                  : typeof page === "string"
                  ? "hover:bg-transparent hover:text-inherit cursor-default border-none"
                  : "text-slate-600 hover:bg-slate-50 border-none"
              }`}
              disabled={typeof page === "string"}
              onClick={() => typeof page === "number" && onPageChange(page)}
            >
              {page}
            </Button>
          ))}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          disabled={!canGoPrevious}
          className={`border-slate-200 flex items-center gap-1 ${
            !canGoPrevious 
              ? "text-slate-400" 
              : "text-slate-600 hover:bg-slate-50"
          }`}
          onClick={onPrevious}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={!canGoNext}
          className={`border-slate-200 flex items-center gap-1 ${
            !canGoNext 
              ? "text-slate-400" 
              : "text-slate-600 hover:bg-slate-50"
          }`}
          onClick={onNext}
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default Pagination;
