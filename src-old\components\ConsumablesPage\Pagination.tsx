import React, { useState } from 'react'
import { MdOutlineKeyboardArrowLeft } from "react-icons/md";
import { MdKeyboardArrowRight } from "react-icons/md";

interface IPagination {
    totalItems: number,
    itemsPerPage: number,
    onPageChange: (page: number) => void
}


const Pagination: React.FC<IPagination> = ({ totalItems, itemsPerPage, onPageChange }) => {


    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        onPageChange(page); // Call the passed function to inform parent component
    };

    const renderPageNumbers = () => {
        const pages = [];
        for (let i = 1; i <= totalPages; i++) {
            pages.push(
                <button
                    key={i}
                    onClick={() => handlePageChange(i)}
                    className={`pagination-item ${currentPage === i ? 'active' : ''}`}
                >
                    {i}
                </button>
            );
        }
        return pages;
    };

    return (
        <div className='flex gap-2 border'>
            <MdOutlineKeyboardArrowLeft size={32}
                onClick={() => handlePageChange(currentPage - 1)}
            //  disabled={currentPage === 1}
            />
            <div>
                {renderPageNumbers()}
            </div>
            <MdKeyboardArrowRight size={32}
                onClick={() => handlePageChange(currentPage + 1)}
            // disabled={currentPage === totalPages}
            />
        </div>
    )
}

export default Pagination



