import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { z } from "zod";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetDescription,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Resource, ResourceFormData } from "@/types/resource";
import type { ResourceType } from "@/types/enums";
import { createResource, updateResource } from "@/services/api";
import ImageUploader from "./ImageUploader";

const resourceSchema = z.object({
  name: z.string().min(1, "Resource name is required"),
  resourceCode: z.string().min(1, "Resource code is required"),
  resourceCategory: z.string().min(1, "Category is required"),
  resourceType: z.enum(["consumable", "asset"]),
  // unitType: z.enum(["individual", "pack"]),
  minInventoryQty: z.number().min(0, "Min quantity must be non-negative"),
  maxInventoryQty: z.number().min(0, "Max quantity must be non-negative"),
  unitSize: z.number().min(1, "Unit size must be at least 1"),
  imageUrl: z.string().optional(),
});

interface AddOrEditResourceDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resource?: Resource | null;
  onResourceUpdate: () => void;
}

const AddOrEditResourceDrawer = ({
  open,
  onOpenChange,
  resource,
  onResourceUpdate,
}: AddOrEditResourceDrawerProps) => {
  const [formData, setFormData] = useState<ResourceFormData>({
    name: "",
    resourceCode: "",
    resourceCategory: "",
    resourceType: "consumable",
    // unitType: "individual",
    minInventoryQty: 0,
    maxInventoryQty: 0,
    unitSize: 1,
    imageUrl: "",
  });
  const [errors, setErrors] = useState<z.ZodError | null>(null);

  const isEditMode = !!resource;

  useEffect(() => {
    if (isEditMode) {
      setFormData({
        name: resource.name,
        resourceCode: resource.resourceCode,
        resourceCategory: resource.resourceCategory || "",
        resourceType: resource.resourceType,
        // unitType: resource.unitType,
        minInventoryQty: resource.minInventoryQty || 0,
        maxInventoryQty: resource.maxInventoryQty || 0,
        unitSize: resource.unitSize || 1,
        imageUrl: resource.imageUrl || "",
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: "",
        resourceCode: "",
        resourceCategory: "",
        resourceType: "consumable",
        // unitType: "individual",
        minInventoryQty: 0,
        maxInventoryQty: 0,
        unitSize: 1,
        imageUrl: "",
      });
    }
  }, [resource, isEditMode]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleImageCropped = (croppedImage: string) => {
    setFormData((prev) => ({ ...prev, imageUrl: croppedImage }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = resourceSchema.safeParse(formData);

    if (!result.success) {
      setErrors(result.error);
      toast.error("Please fix the errors in the form");
      return;
    }

    setErrors(null);
    const resourceData = result.data;

    try {
      if (isEditMode && resource) {
        await updateResource(resource.id, resourceData);
        toast.success("Resource updated successfully");
      } else {
        await createResource(resourceData);
        toast.success("Resource created successfully");
      }
      onResourceUpdate();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save resource:", error);
      toast.error("Failed to save resource");
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-[50vw]  [&>button]:hidden p-10">
        <div className="flex flex-col h-full">
          {/* Custom Close Button */}
          <div className=" pb-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="w-fit p-2 bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Header */}
          <SheetHeader className="mt-12  p-0">
            <SheetTitle className="text-2xl font-semibold text-gray-900 ">
              {isEditMode ? "Edit Resource" : "Add New Resource"}
            </SheetTitle>
            <SheetDescription className="text-sm text-gray-600 ">
              {isEditMode
                ? "Update the details of this resource."
                : "Create a new resource in your inventory"}
            </SheetDescription>
          </SheetHeader>

          {/* Form Content */}
          <div className="flex-1  pb-6 overflow-y-auto mt-10">
            <form onSubmit={handleSubmit} className="space-y-6">
              <ImageUploader
                onImageCropped={handleImageCropped}
                initialImage={formData.imageUrl}
              />

              {/* Resource Name and Code */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-10">
                <div className="space-y-2">
                  <Label
                    htmlFor="name"
                    className="text-sm font-medium text-gray-700"
                  >
                    Resource Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter resource name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className="w-full h-11"
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="code"
                    className="text-sm font-medium text-gray-700"
                  >
                    Resource Code
                  </Label>
                  <Input
                    id="code"
                    placeholder="Enter resource code"
                    value={formData.resourceCode}
                    onChange={(e) =>
                      handleInputChange("resourceCode", e.target.value)
                    }
                    className="w-full h-11"
                  />
                </div>
              </div>

              {/* Category and Type */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Unit Type
                  </Label>
                  <Select
                    value={formData.unitType}
                    onValueChange={(value) =>
                      handleInputChange("unitType", value as UnitType)
                    }
                  >
                    <SelectTrigger className="w-full !h-11">
                      <SelectValue placeholder="Select Unit Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="individual">Individual</SelectItem>
                      <SelectItem value="pack">Pack</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Category
                  </Label>
                  <Select
                    value={formData.resourceCategory}
                    onValueChange={(value) =>
                      handleInputChange("resourceCategory", value)
                    }
                  >
                    <SelectTrigger className="w-full !h-11">
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tools">Tools</SelectItem>
                      <SelectItem value="materials">Materials</SelectItem>
                      <SelectItem value="equipment">Equipment</SelectItem>
                      <SelectItem value="supplies">Supplies</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Type
                  </Label>
                  <Select
                    value={formData.resourceType}
                    onValueChange={(value) =>
                      handleInputChange("resourceType", value as ResourceType)
                    }
                  >
                    <SelectTrigger className="w-full !h-11">
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="consumable">Consumable</SelectItem>
                      <SelectItem value="asset">Asset</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Min and Max Quantity */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Min Quantity
                  </Label>
                  <Input
                    value={formData.minInventoryQty}
                    onChange={(e) =>
                      handleInputChange(
                        "minInventoryQty",
                        parseInt(e.target.value) || 0
                      )
                    }
                    className="w-full h-11"
                    min="0"
                    type="number"
                    placeholder="Enter Min Quantity"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Max Quantity
                  </Label>
                  <Input
                    value={formData.maxInventoryQty}
                    onChange={(e) =>
                      handleInputChange(
                        "maxInventoryQty",
                        parseInt(e.target.value) || 0
                      )
                    }
                    className="w-full h-11"
                    min="0"
                    type="number"
                    placeholder="Enter Max Quantity"
                  />
                </div>
              </div>

              {errors && (
                <div className="text-red-500">
                  <ul>
                    {errors.issues.map((err) => (
                      <li key={err.path.join(".")}>{err.message}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Submit Button */}
              <div className=" flex justify-end pt-6 ml-4">
                <Button
                  type="submit"
                  className="bg-primary-500 hover:bg-primary-600 text-white py-3 px-8 w-1/2 "
                >
                  {isEditMode ? "Save Changes" : "Add Resource"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default AddOrEditResourceDrawer;
