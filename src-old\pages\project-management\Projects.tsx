import React, { useState, useEffect } from 'react'
import Header from '../../components/general/Header'
import { useAuth } from '../../context/AuthContextProvider'
import { projectService } from '../../services/api'
import { Trash2 } from 'lucide-react'
import { Box, Modal } from '@mui/material'

interface Project {
  id: string;
  projectCode: string;
  projectName: string;
  startDate: Date;
  dueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

const Projects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [newProject, setNewProject] = useState({
    projectCode: '',
    projectName: '',
    startDate: '',
    dueDate: ''
  });
  const [error, setError] = useState('');
  const { token } = useAuth();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  const fetchProjects = async () => {
    try {
      const response = await projectService.get('/projects', token);
      setProjects(response.data);
    } catch (err) {
      console.error('Error fetching projects:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewProject(prev => ({
      ...prev,
      [name]: value
    }));
  };  
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newProject.projectCode.trim() || !newProject.projectName.trim() || !newProject.startDate || !newProject.dueDate) {
      setError('All fields are required');
      return;
    }

    // Validate that due date is after start date
    const startDate = new Date(newProject.startDate);
    const dueDate = new Date(newProject.dueDate);
    
    if (dueDate <= startDate) {
      setError('Due date must be after start date');
      return;
    }

    try {
      const projectData = {
        ...newProject,
        startDate: startDate.toISOString(),
        dueDate: dueDate.toISOString()
      };
      
      await projectService.post('/projects', projectData, token);
      setNewProject({ projectCode: '', projectName: '', startDate: '', dueDate: '' });
      fetchProjects();
      setError('');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create project');
    }
  };

  const openDeleteModal = (id: string) => {
    setProjectToDelete(id);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setProjectToDelete(null);
  };

  const handleDelete = async () => {
    if (!projectToDelete) return;
    
    try {
      await projectService.delete(`/projects/${projectToDelete}`, token);
      fetchProjects(); // Refresh the list after deletion
      closeDeleteModal();
    } catch (err) {
      console.error('Error deleting project:', err);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <main className='bg-mainbg min-h-screen'>
      <Header />

      <section className="bg-white m-4 rounded-xl p-4">
        <h2 className="text-xl font-bold mb-4">Add New Project</h2>

        {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded mb-4">{error}</div>}        <form onSubmit={handleSubmit} className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Project Code</label>
            <input
              type="text"
              name="projectCode"
              value={newProject.projectCode}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter project code"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
            <input
              type="text"
              name="projectName"
              value={newProject.projectName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter project name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              name="startDate"
              value={newProject.startDate}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
            <input
              type="date"
              name="dueDate"
              value={newProject.dueDate}
              onChange={handleInputChange}
              min={newProject.startDate}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            type="submit"
            className="bg-pickTBlue hover:bg-blue-700 text-white font-medium py-2 px-4 rounded h-fit"
          >
            Add Project
          </button>
        </form>
      </section>

      <section className='bg-white m-4 rounded-xl p-4'>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Projects List</h2>
          <input
            type="text"
            placeholder="Search by Project Name or Code"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Code</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {projects.length > 0 ? (
                projects
                  .filter(
                    (project) =>
                      project.projectName
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase()) ||
                      project.projectCode
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase())
                  )
                  .map((project) => (
                    <tr key={project.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">{project.projectCode}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{project.projectName}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                      {new Date(project.startDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {new Date(project.dueDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {new Date(project.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button 
                        onClick={() => openDeleteModal(project.id)}
                        className="bg-[#707070] hover:bg-gray-600 p-2 rounded-lg"
                      >
                        <Trash2 className="text-white h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">No projects found</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </section>

      {/* Delete Confirmation Modal */}
      <Modal
        open={deleteModalOpen}
        onClose={closeDeleteModal}
        aria-labelledby="delete-confirmation-modal"
      >
        <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[90%] max-w-md flex flex-col gap-4">
          <h3 className="text-lg font-semibold">Confirm Delete</h3>
          <p>Are you sure you want to delete this project? This action cannot be undone.</p>
          <div className="flex justify-end gap-3 mt-2">
            <button 
              onClick={closeDeleteModal}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
            >
              Cancel
            </button>
            <button 
              onClick={handleDelete}
              className="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </Box>
      </Modal>
    </main>
  )
}

export default Projects
