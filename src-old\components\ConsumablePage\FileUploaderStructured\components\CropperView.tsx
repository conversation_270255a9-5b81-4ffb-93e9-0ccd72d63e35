import React from 'react';
import Cropper from "react-easy-crop";
import { CropperViewProps } from '../types';
import { CROP_CONFIG } from '../constants';
import { cropperStyles, cropperClasses } from '../styles';
import ZoomSlider from './ZoomSlider';

const CropperView: React.FC<CropperViewProps> = ({
    imageToCrop,
    crop,
    zoom,
    onCropChange,
    onCropComplete,
    onZoomChange,
    onMediaLoaded
}) => {
    return (
        <div className='flex flex-col gap-3'>
            <div className='relative w-80 h-60 rounded-md border-2 border-dashed border-pickTBlue overflow-hidden bg-white'>
                <Cropper
                    image={imageToCrop}
                    crop={crop}
                    zoom={zoom}
                    transform=""
                    cropSize={CROP_CONFIG.SIZE}
                    aspect={CROP_CONFIG.ASPECT_RATIO}
                    onCropChange={onCropChange}
                    onCropComplete={onCropComplete}
                    onZoomChange={onZoomChange}
                    style={cropperStyles}
                    classes={cropperClasses}
                    onMediaLoaded={onMediaLoaded}
                />
            </div>
            <ZoomSlider zoom={zoom} onZoomChange={onZoomChange} />
        </div>
    );
};

export default CropperView;
