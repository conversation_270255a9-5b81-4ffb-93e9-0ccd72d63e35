import { CloudUpload, Upload, Edit } from 'lucide-react';
import React, { useRef, useState } from 'react'
import <PERSON>rop<PERSON> from "react-easy-crop";

interface IFileUploader {
    selectedImage: string
    shareFile: (data: File) => void
    isEditable: boolean
    onImageUpload?: (url: string) => void
}

const FileUploader = ({ selectedImage, shareFile, isEditable, onImageUpload }: IFileUploader) => {
    const hiddenFileInput = useRef<HTMLInputElement>(null)
    const [imageToCrop, setImageToCrop] = useState("")
    const [crop, setCrop] = useState({ x: 0, y: 0 })
    const [zoom, setZoom] = useState(0.3)
    const [cropped, setCropped] = useState({
        x: 0,
        y: 0,
        width: 0,
        height: 0,
    })
    const CONTAINER_HEIGHT = 800;    
    const handleClick = () => {
        if (!imageToCrop) {
            hiddenFileInput.current?.click()
        }
    }

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const file: File = event.target.files![0];
        if (file) {
            let previewUrl = URL.createObjectURL(file);
            setImageToCrop(previewUrl);
        }
    }

    const handleCrop = () => {
        if (cropped) {
            const canvas = document.createElement("canvas");
            canvas.width = cropped.width;
            canvas.height = cropped.height;
            const ctx = canvas.getContext("2d");

            const image = new Image();
            image.src = imageToCrop;

            image.onload = () => {
                if (ctx) {
                    ctx.drawImage(
                        image,
                        cropped.x,
                        cropped.y,
                        cropped.width,
                        cropped.height,
                        0,
                        0,
                        cropped.width,
                        cropped.height
                    );

                    canvas.toBlob(
                        async (blob) => {
                            if (blob) {
                                const croppedFile = new File([blob], "cropped-image.jpg", {
                                    type: "image/jpeg",
                                });
                                const previewUrl = URL.createObjectURL(blob);
                                if (onImageUpload) {
                                    onImageUpload(previewUrl);
                                }
                                shareFile(croppedFile);
                                setImageToCrop("");
                            }
                        },
                        "image/jpeg",
                        0.95
                    );
                }
            };
        }
    };

    const handleCropClose = () => {
        setImageToCrop("");
    };

    const onCropComplete = (croppedArea: any, croppedAreaPixels: any) => {
        setCropped(croppedAreaPixels);
    };

    const handleOnCropChange = (e: any) => {
        setCrop(e);
    };    return (
        <>            <style>{`
                .cropper-container .reactEasyCrop_Container {
                    background: white !important;
                }
                .cropper-container .reactEasyCrop_Container::before {
                    background: white !important;
                }
                .cropper-container .reactEasyCrop_Container::after {
                    background: white !important;
                }
                .cropper-container {
                    background: white !important;
                }
                
                /* Custom slider styles */
                .slider::-webkit-slider-thumb {
                    appearance: none;
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #3b82f6;
                    cursor: pointer;
                    border: 2px solid #ffffff;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }
                
                .slider::-moz-range-thumb {
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #3b82f6;
                    cursor: pointer;
                    border: 2px solid #ffffff;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }
                
                .slider::-webkit-slider-track {
                    background: #e5e7eb;
                    border-radius: 5px;
                }
                
                .slider::-moz-range-track {
                    background: #e5e7eb;
                    border-radius: 5px;
                }
            `}</style>
            <div className="flex items-start gap-4">
            <div onClick={handleClick} className={`flex flex-col items-start p-2 w-fit justify-center ${selectedImage || imageToCrop ? "" : "rounded-md border-2 border-dashed border-pickTBlue"}  h-full border-gray-400 bg-white`}>
                <div className='' >                      {imageToCrop ? (
                        // Cropping mode
                        <div className='flex flex-col gap-3'>
                            <div className='relative w-80 h-60 rounded-md border-2 border-dashed border-pickTBlue overflow-hidden bg-white'>
                                <Cropper
                                    image={imageToCrop}
                                    crop={crop}
                                    zoom={zoom}
                                    transform=""
                                    cropSize={{ width: 250, height: 250 }}
                                    aspect={4 / 3}
                                    onCropChange={handleOnCropChange}
                                    onCropComplete={onCropComplete}
                                    onZoomChange={setZoom}
                                    style={{
                                        containerStyle: {
                                            background: 'white',
                                            width: '100%',
                                            height: '100%',
                                        },
                                        mediaStyle: {
                                            display: 'block',
                                        },
                                        cropAreaStyle: {
                                            border: '2px solid #3b82f6',
                                        }
                                    }}
                                    classes={{
                                        containerClassName: 'cropper-container',
                                        mediaClassName: 'cropper-media'
                                    }}                                
                                    onMediaLoaded={(mediaSize) => {
                                        // Adapt zoom based on media size to fit max height
                                        setZoom(CONTAINER_HEIGHT / mediaSize.naturalHeight);
                                    }}
                                />
                            </div>
                            {/* Zoom Slider */}
                            <div className='w-80 px-2'>
                                <label className='block text-sm font-medium text-gray-700 mb-2'>
                                    Zoom: {Math.round(zoom * 100)}%
                                </label>
                                <input
                                    type="range"
                                    min={0.1}
                                    max={3}
                                    step={0.1}
                                    value={zoom}
                                    onChange={(e) => setZoom(Number(e.target.value))}
                                    className='w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider'
                                />
                            </div>
                        </div>                    ) : selectedImage ? (
                        <div className='relative w-full'>
                            <img src={selectedImage} alt="Uploaded" className="w-full max-h-32  rounded-md border-2 border-dashed  border-pickTBlue " />
                            <button className="absolute top-1 right-1 bg-black bg-opacity-70 hover:bg-opacity-90 text-white p-2 rounded-full shadow-md transition-all duration-200">
                                <Edit className="h-4 w-4" />
                            </button>
                        </div>
                    ) : (
                        <div className='flex flex-col justify-center items-center '>
                            <Upload className="h-6 w-6 text-gray-500 mb-2" />
                            <label className="text-sm text-gray-600 cursor-pointer text-center hover:text-gray-800">
                                Click to upload
                            </label>
                        </div>
                    )}
                </div>
                <input
                    type="file"
                    ref={hiddenFileInput}
                    accept="image/*"
                    className="hidden"
                    onChange={handleChange}
                    disabled={!isEditable}
                />
            </div>
            
            {/* Buttons outside the dashed div - shown only when cropping */}
            {imageToCrop && (
                <div className="flex flex-col gap-2 mt-2">
                    <button
                        className="border px-4 py-2 rounded bg-white cursor-pointer border-gray-300 hover:bg-gray-50 text-sm font-medium"
                        onClick={handleCropClose}
                    >
                        Cancel
                    </button>
                    <button
                        className="bg-pickTBlue text-white px-4 py-2 rounded-md hover:bg-blue-600 text-sm font-medium"
                        onClick={handleCrop}
                    >
                        Crop & Upload
                    </button>                </div>
            )}
        </div>
        </>
    )
}

export default FileUploader

