import type { ReactNode } from "react";

interface LoadingStateProps {
  message?: string;
}

interface ErrorStateProps {
  error: Error;
}

export const LoadingState = ({ message = "Loading..." }: LoadingStateProps) => (
  <main className="p-10">
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-slate-600">{message}</p>
      </div>
    </div>
  </main>
);

export const ErrorState = ({ error }: ErrorStateProps) => (
  <main className="p-10">
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <div className="text-red-500 text-xl mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-slate-900 mb-2">Something went wrong</h3>
        <p className="text-sm text-slate-500">Error: {error.message}</p>
      </div>
    </div>
  </main>
);

interface ResourcePageLayoutProps {
  children: ReactNode;
}

export const ResourcePageLayout = ({ children }: ResourcePageLayoutProps) => (
  <main className="p-10 bg-[#FCFCFC]">
    {children}
  </main>
);
