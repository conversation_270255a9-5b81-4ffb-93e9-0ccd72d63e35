import { Button } from "@mui/material";
import { useEffect, useState } from "react";
import FileUploader from "./FileUploader";
import InputField from "./InputField";
import { useNavigate } from "react-router-dom";
import { consumableService, fileUploadService } from "../../services/api";
import { useAuth } from "../../context/AuthContextProvider";

interface IGProductInfo {
  id?: string;
  setConsumableData: (data: any) => void;
}

interface IMessage {
  errorMsg: string;
  errorOf: string;
}

const GProductInfo = ({ id, setConsumableData }: IGProductInfo) => {
  const navigate = useNavigate();
  const [consumalbe, setConsumable] = useState({
    id: "",
    resourceCode: "",
    productName: "",
    category: "",
    resourceType: "",
    quantity: "",
    minInventoryQty: "",
    maxInventoryQty: "",
    imageUrl: "",
    urlToUpload: "",
  });
  const [isEditable, setIsEditable] = useState(true);
  const [catergories, setCategories] = useState<string[]>();
  const resourceTypes = ["Asset", "Consumable"];
  const [putStatus, setPutStatus] = useState(false);
  const [error, setError] = useState<
    { message: IMessage[]; type: string; errorOf: string } | undefined
  >({
    errorOf: "",
    message: [
      {
        errorMsg: "",
        errorOf: "",
      },
    ],
    type: "",
  });
  const { token } = useAuth();

  // Helper function to get error message for a specific field
  const getFieldError = (fieldName: string): string => {
    if (error?.errorOf !== "itemError") return "";
    return (
      error?.message.find((msg) => msg.errorOf === fieldName)?.errorMsg || ""
    );
  };

  // Helper function to check if field has error
  const hasFieldError = (fieldName: string): boolean => {
    if (error?.errorOf !== "itemError") return false;
    return error?.message.some((msg) => msg.errorOf === fieldName);
  };
  const uploadImage = async (data: File) => {
    // const upload = await axios.post("/api/file-upload", formData)
    const upload = await fileUploadService.post("/file-upload", data, token);

    // ...existing code...
    return upload.data.url;
  };

  const handleImageProcessing = async (data: File) => {
    try {
      const imageUrl = await uploadImage(data);
      handleConsumableData("urlToUpload", imageUrl);
    } catch (error) {
      console.error("Failed to upload image:", error);
    }
  };

  const handleImagePreview = (previewUrl: string) => {
    handleConsumableData("imageUrl", previewUrl);
  };

  const handleConsumableData = (key: string, value: string | File) => {
    // ...existing code...
    setConsumable((prev) => {
      return {
        ...prev,
        [key]: value,
      };
    });
  };

  const saveConsumable = async () => {
    const consumableD = {
      name: consumalbe.productName,
      resourceType: consumalbe.resourceType.toLowerCase(),
      // unitSize: parseInt(consumalbe.quantity),
      unitSize: 1,
      resourceCode: consumalbe.resourceCode,
      resourceCategory: consumalbe.category,
      imageUrl: consumalbe.urlToUpload,
      minInventoryQty:
        consumalbe.minInventoryQty !== ""
          ? parseInt(consumalbe.minInventoryQty)
          : undefined,
      maxInventoryQty:
        consumalbe.maxInventoryQty !== ""
          ? parseInt(consumalbe.maxInventoryQty)
          : undefined,
    };

    function validateConsumable(consumableD: any) {
      const errors = [];

      if (!consumableD.imageUrl || typeof consumableD.imageUrl !== "string") {
        errors.push({ errorMsg: "Image is required", errorOf: "image" });
      }

      if (
        !consumableD.resourceCode ||
        typeof consumableD.resourceCode !== "string"
      ) {
        errors.push({
          errorMsg: "Resource Code is required",
          errorOf: "resourceCode",
        });
      }

      if (!consumableD.name || typeof consumableD.name !== "string") {
        errors.push({ errorMsg: "Name is required", errorOf: "productName" });
      }

      if (
        !consumableD.resourceCategory ||
        typeof consumableD.resourceCategory !== "string"
      ) {
        errors.push({
          errorMsg: "Category is required",
          errorOf: "resourceCategory",
        });
      }

      if (
        !consumableD.resourceType ||
        typeof consumableD.resourceType !== "string"
      ) {
        errors.push({ errorMsg: "Type is required", errorOf: "productType" });
      }

      // Validate minInventoryQty
      if (
        consumableD.minInventoryQty !== undefined &&
        consumableD.minInventoryQty !== "" &&
        (!Number.isInteger(Number(consumableD.minInventoryQty)) ||
          Number(consumableD.minInventoryQty) < 0)
      ) {
        errors.push({
          errorMsg: "Minimum inventory quantity must be a non-negative integer",
          errorOf: "minInventoryQty",
        });
      }

      // Validate maxInventoryQty
      if (
        consumableD.maxInventoryQty !== undefined &&
        consumableD.maxInventoryQty !== "" &&
        (!Number.isInteger(Number(consumableD.maxInventoryQty)) ||
          Number(consumableD.maxInventoryQty) <= 0)
      ) {
        errors.push({
          errorMsg: "Maximum inventory quantity must be a positive integer",
          errorOf: "maxInventoryQty",
        });
      }

      // Check maxInventoryQty > minInventoryQty
      if (
        consumableD.minInventoryQty !== undefined &&
        consumableD.maxInventoryQty !== undefined &&
        consumableD.minInventoryQty !== "" &&
        consumableD.maxInventoryQty !== "" &&
        Number(consumableD.maxInventoryQty) <=
          Number(consumableD.minInventoryQty)
      ) {
        errors.push({
          errorMsg:
            "Maximum inventory quantity must be greater than minimum inventory quantity",
          errorOf: "maxInventoryQty",
        });
      }

      return errors.length > 0 ? { valid: false, errors } : { valid: true };
    }

    const error = validateConsumable(consumableD);
    if (!error.valid && error.errors) {
      handleError(error.errors, "error", "itemError");
      return;
    }
    // ...existing code...

    try {
      if (putStatus) {
        // ...existing code...
        // const response = await axios.put(`/api/resources/${consumalbe?.id}`, consumableD)
        const response = await consumableService.put(
          `/resources/${consumalbe?.id}`,
          consumableD,
          token
        );
        // ...existing code...
        if (response.status === 200) {
          setIsEditable(false);
          setConsumableData(response.data);
          handleError(
            [
              {
                errorMsg: "Resource Updated Successfully",
                errorOf: "productActionUpdate",
              },
            ],
            "success",
            "productAction"
          );
        }
      } else {
        // ...existing code...
        // const response = await axios.post("/resources", consumableD)
        const response = await consumableService.post(
          "/resources",
          consumableD,
          token
        );
        if (response.status === 201) {
          setIsEditable(false);
          setConsumableData(response.data);
          handleConsumableData("id", response.data.id);
          handleError(
            [
              {
                errorMsg: "Resource Created Successfully",
                errorOf: "productActionSucces",
              },
            ],
            "success",
            "productAction"
          );
        }
      }
    } catch (error: any) {
      // setError(error.response.data.message)
      handleError(
        [
          {
            errorMsg: error.response.data.message,
            errorOf: "productActionError",
          },
        ],
        "error",
        "productAction"
      );
      console.error("Error uploading data");
      console.error(error);
    }
  };

  const fetchConsumable = async (id: string) => {
    // const url =
    // const consumable = await axios.get(url)
    const consumable = await consumableService.get(`/resources/${id}`, token);
    if (consumable.status === 200) {
      // ...existing code...
      const consumableData = consumable.data;

      setConsumable({
        id: consumableData.id,
        resourceCode: consumableData.resourceCode,
        productName: consumableData.name,
        category: consumableData.resourceCategory,
        resourceType: consumableData.resourceType.charAt(0).toUpperCase() + consumableData.resourceType.slice(1),
        quantity: consumableData.unitSize,
        imageUrl: consumableData.imageUrl,
        urlToUpload: consumableData.imageUrl,
        minInventoryQty: consumableData.minInventoryQty,
        maxInventoryQty: consumableData.maxInventoryQty,
      });
      setConsumableData({
        id: consumableData.id,
        resourceCode: consumableData.resourceCode,
        productName: consumableData.name,
        category: consumableData.resourceCategory,
        resourceType: consumableData.resourceType.charAt(0).toUpperCase() + consumableData.resourceType.slice(1),
        quantity: consumableData.unitSize,
        imageUrl: consumableData.imageUrl,
        urlToUpload: consumableData.imageUrl,
        minInventoryQty: consumableData.minInventoryQty,
        maxInventoryQty: consumableData.maxInventoryQty,
      });
      setIsEditable(false);
    }
  };
  const fetchCategory = async () => {
    // const categories = await axios.get("/api/resources/categories")
    const categories = await consumableService.get(
      "/resources/categories",
      token
    );
    if (categories.status === 200) {
      setCategories(categories.data);
    }
  };
  const handleEdit = () => {
    setIsEditable(true);
    setPutStatus(true);
  };

  const handleError = (message: IMessage[], type: string, errorOf: string) => {
    setError({ message, type, errorOf });
    setTimeout(() => {
      setError(undefined);
    }, 3000);
  };

  useEffect(() => {
    if (id) {
      fetchConsumable(id);
    }
    fetchCategory();
  }, []);
  return (
    <section className="flex flex-col rounded-xl bg-white font-nunito relative shadow-sm border border-gray-100">
      {/* Non-editable overlay */}
      {!isEditable && (
        <div className="absolute inset-0 bg-white/60 backdrop-blur-[1px] z-30 rounded-xl" />
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <h1 className="text-xl font-bold text-gray-900">
            Resource Information
          </h1>
          {error?.message && error?.errorOf === "productAction" && (
            <div
              className={`px-3 py-1 text-sm text-white rounded-full font-medium ${
                error.type === "error" ? "bg-red-500" : "bg-green-500"
              }`}
            >
              {error.message[0].errorMsg}
            </div>
          )}
        </div>

        <div className="flex gap-3">
          {isEditable ? (
            <div className="flex gap-3">
              <Button
                variant="outlined"
                onClick={() => navigate("/")}
                sx={{
                  fontFamily: "Lexend, sans-serif",
                  fontWeight: 500,
                  fontSize: "0.875rem",
                  textTransform: "none",
                  borderColor: "#d1d5db",
                  color: "#374151",
                  "&:hover": {
                    borderColor: "#9ca3af",
                    backgroundColor: "#f9fafb",
                  },
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={saveConsumable}
                sx={{
                  fontFamily: "Lexend, sans-serif",
                  fontWeight: 500,
                  fontSize: "0.875rem",
                  backgroundColor: "#2563eb",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#1d4ed8",
                  },
                }}
              >
                Save Resource
              </Button>
            </div>
          ) : (
            <Button
              variant="contained"
              onClick={handleEdit}
              sx={{
                fontFamily: "Lexend, sans-serif",
                fontWeight: 500,
                fontSize: "0.875rem",
                backgroundColor: "#2563eb",
                textTransform: "none",
                zIndex: 40,
                "&:hover": {
                  backgroundColor: "#1d4ed8",
                },
              }}
            >
              Edit Resource
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* <div className="grid grid-cols-1 xl:grid-cols-4 gap-6"> */}
        <div className="flex flex-col gap-3">
          {/* Image Upload Section */}
          <div className="">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Resource Image
              </label>{" "}
              <FileUploader
                isEditable={isEditable}
                selectedImage={consumalbe.imageUrl}
                shareFile={handleImageProcessing}
                onImageUpload={handleImagePreview}
              />
              {error?.errorOf === "itemError" && (
                <p className="text-red-500 text-sm text-center">
                  {getFieldError("image")}
                </p>
              )}
            </div>
          </div>

          {/* Form Fields */}
          <div className="xl:col-span-3">
            <div className="bg-gray-50 rounded-xl p-6 space-y-6">
              {/* Row 1: Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  {" "}
                  <InputField
                    label="Resource Code"
                    value={consumalbe.resourceCode}
                    disabled={!isEditable}
                    onChange={(value) =>
                      handleConsumableData("resourceCode", value)
                    }
                    error={
                      hasFieldError("resourceCode")
                        ? [getFieldError("resourceCode")]
                        : [""]
                    }
                  />
                </div>
                <div className="space-y-2">
                  {" "}
                  <InputField
                    label="Resource Name"
                    value={consumalbe.productName}
                    disabled={!isEditable}
                    onChange={(value) =>
                      handleConsumableData("productName", value)
                    }
                    error={
                      hasFieldError("productName")
                        ? [getFieldError("productName")]
                        : [""]
                    }
                  />
                </div>
              </div>

              {/* Row 2: Category and Type */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Category <span className="text-red-500">*</span>
                  </label>
                  <select
                    className="w-full rounded-lg border border-gray-300 px-3 py-2.5 text-sm font-medium bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    value={consumalbe.category}
                    onChange={(e) =>
                      handleConsumableData("category", e.target.value)
                    }
                    disabled={!isEditable}
                  >
                    <option value="">Choose Category</option>
                    {catergories?.map((category: string) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>{" "}
                  {hasFieldError("resourceCategory") && (
                    <p className="text-red-500 text-xs">
                      {getFieldError("resourceCategory")}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    className="w-full rounded-lg border border-gray-300 px-3 py-2.5 text-sm font-medium bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    value={consumalbe.resourceType}
                    onChange={(e) =>
                      handleConsumableData("resourceType", e.target.value)
                    }
                    disabled={!isEditable}
                  >
                    <option value="">Choose Type</option>
                    {resourceTypes?.map((type: string) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>{" "}
                  {hasFieldError("productType") && (
                    <p className="text-red-500 text-xs">
                      {getFieldError("productType")}
                    </p>
                  )}
                </div>
              </div>

              {/* Row 3: Inventory Quantities */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <InputField
                    label="Minimum Quantity"
                    type="number"
                    value={consumalbe.minInventoryQty}
                    disabled={!isEditable}
                    onChange={(value) =>
                      handleConsumableData("minInventoryQty", value)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <InputField
                    label="Maximum Quantity"
                    type="number"
                    value={consumalbe.maxInventoryQty}
                    disabled={!isEditable}
                    onChange={(value) =>
                      handleConsumableData("maxInventoryQty", value)
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GProductInfo;
