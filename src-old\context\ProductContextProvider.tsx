import React, { createContext, useState } from "react";

interface ProductContextType {
  product: any; // Replace 'any' with your specific product type
  setProduct: React.Dispatch<React.SetStateAction<any>>;
}

interface ProductProviderProps {
  children: React.ReactNode;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

const ProductContextProvider: React.FC<ProductProviderProps> = ({ children }) => {
  const [product, setProduct] = useState({});

  const value = {
    product,
    setProduct
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};

export default ProductContextProvider;
