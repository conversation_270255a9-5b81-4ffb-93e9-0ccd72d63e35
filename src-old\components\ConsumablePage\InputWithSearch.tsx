import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { IoSearch } from "react-icons/io5";
import { FaPlus } from "react-icons/fa";
import { useAuth } from "../../context/AuthContextProvider";
import { supplierService } from "../../services/api";

interface IInputWithSearch {
    selected: (supplier: any) => void
    addSupplierHandler: (status: boolean) => void
}



const InputWithSearch = ({ selected, addSupplierHandler }: IInputWithSearch) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [isDropped, setIsDropped] = useState(false)
    const [suppliers, setSuppliers] = useState([])
    const [filtered, setFilterd] = useState([])
    const {token} = useAuth()

    const fetchSuppliers = async () => {
        // const suppliers = await axios.get("/api/suppliers")
        const suppliers = await supplierService.get("/suppliers",token)
        setSuppliers(suppliers.data)
        setFilterd(suppliers.data)
    }

    const filterSupplyData = (value: string) => {
        const filtered = suppliers.filter((sup: any) => sup.name.toLowerCase().includes(value.toLowerCase()))
        setFilterd(filtered)
    }

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!isDropped) {
            setIsDropped(true)
        }
        setSearchTerm(e.target.value);
        filterSupplyData(e.target.value)

    };

    const handleSelection = (item: any) => {
        // ...existing code...
        setSearchTerm(item.name)
        setIsDropped(!isDropped)
        selected(item)
    }

    const handleAddSupplier = () => {
        setIsDropped(false)
        addSupplierHandler(true)
    }

    useEffect(() => {
     
        fetchSuppliers()
    }, [])

    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClick = (e: MouseEvent) => {
            if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
                setIsDropped(false);
            }
        };

        if (isDropped) {
            document.addEventListener('click', handleClick);
        }

        return () => {
            document.removeEventListener('click', handleClick);
        };
    }, [isDropped]);


    return (
        <div className='relative' ref={containerRef} >
            <div className='flex gap-2 items-center border rounded-md h-11 px-2 w-full cursor-pointer' onClick={() => setIsDropped(!isDropped)} >
                <input
                    type="text"
                    className='w-full py-2 px-3 outline-none text-xs font-normal'
                    value={searchTerm}
                    onChange={handleSearch}
                    placeholder="Search..."
                />
                <IoSearch size={30} />
            </div>
            {(isDropped && filtered.length > 0) ? <div className={`${filtered.length > 3 ? "h-32 overflow-hidden " : "h-fit"} absolute top-14 left-0  w-full z-50 bg-[#F7F9FF] border border-gray-300 p-3 rounded-md`}>
                <section >
                    {
                        filtered?.map((item: any) => {
                            return (
                                <div key={item.id} className='flex gap-2  hover:bg-slate-200 p-2' onClick={() => handleSelection(item)}>
                                    <input type="radio" id={item.name} />
                                    <label htmlFor={item.name}>{item.name}</label>
                                </div>
                            )
                        })
                    }
                </section>
            </div> : (filtered.length <= 0 && isDropped) ? <div className='absolute top-14 h-fit left-0  w-full z-50 bg-[#F7F9FF] p-3 rounded-md'>
                <div className="flex items-center justify-center  h-full">
                    <div className="flex gap-2 items-center">
                        <h3 className='cursor-pointer text-lg font-semibold' onClick={handleAddSupplier}>Add Supplier</h3>
                        <FaPlus />
                    </div>
                </div>
            </div> : null}
        </div>
    );
};

export default InputWithSearch