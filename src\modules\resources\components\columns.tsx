"use client"

import type { ColumnDef } from "@tanstack/react-table"

import type { Resource } from "@/types/resource"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

import { DataTableRowActions } from "./DataTableRowActions"

interface ColumnsProps {
  onResourceUpdate: () => void;
}

export const columns = ({ onResourceUpdate }: ColumnsProps): ColumnDef<Resource>[] => [
  {
    accessorKey: "name",
    header: "Item Name",
    size: 250,
    cell: ({ row }) => (
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10 rounded border border-[#DFDFDF]">
          <AvatarImage src={row.original.imageUrl} alt={row.original.name} />
          <AvatarFallback>{row.original.name.charAt(0)}</AvatarFallback>
        </Avatar>
        {row.original.name}
      </div>
    ),
  },
  {
    accessorKey: "resourceCode",
    header: "Item Code",
    size: 150,
  },
  {
    accessorKey: "resourceType",
    header: "Type",
    size: 150,
    cell: ({ row }) => (
      <div className={`px-3 py-2 w-fit rounded font-semibold uppercase ${
        row.original.resourceType === 'asset' 
          ? 'bg-primary-100 text-primary-500' 
          : 'bg-warning-100 text-warning-500'
      }`}>
        {row.original.resourceType}
      </div>
    ),
  },
  {
    accessorKey: "unitType",
    header: "Unit",
    size: 150,
    cell: ({ row }) => (
      <div className="capitalize">
        {row.original.unitType}
      </div>
    ),
  },
  {
    accessorKey: "quantity",
    header: "Quantity",
    size: 100,
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} onResourceUpdate={onResourceUpdate} />,
    size: 100,
  },
]
