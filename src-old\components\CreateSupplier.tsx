import React, { useState } from 'react';
import { ISupplier } from '../models/Supplier';

interface CreateSupplierProps {
  onSupplierCreated: () => void;
}

const CreateSupplier: React.FC<CreateSupplierProps> = ({ onSupplierCreated }) => {
  const [supplier, setSupplier] = useState<Omit<ISupplier, 'id'>>({
    name: '',
    code: '',
    contactEmail: '',
    phoneNumber: '',
    address: '',
    contactPerson: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSupplier((prevSupplier) => ({
      ...prevSupplier,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/suppliers/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(supplier),
      });
      if (response.ok) {
        // ...existing code...
        setSupplier({
          name: '',
          code: '',
          contactEmail: '',
          phoneNumber: '',
          address: '',
          contactPerson: '',
        });
        onSupplierCreated(); // Notify parent component
      } else {
        console.error('Failed to create supplier');
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit} className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
        <input className='for-input' type="text" name="name" value={supplier.name} onChange={handleChange} placeholder="Name" required />
        <input className='for-input' type="text" name="code" value={supplier.code} onChange={handleChange} placeholder="Code" required />
        <input className='for-input' type="email" name="contactEmail" value={supplier.contactEmail} onChange={handleChange} placeholder="Contact Email" required />
        <input className='for-input' type="tel" name="phoneNumber" value={supplier.phoneNumber} onChange={handleChange} placeholder="Phone Number" required />
        <input className='for-input' type="text" name="address" value={supplier.address} onChange={handleChange} placeholder="Address" required />
        <input className='for-input' type="text" name="contactPerson" value={supplier.contactPerson} onChange={handleChange} placeholder="Contact Person" required />
        <div className='sm:col-span-2 lg:col-span-3 flex justify-end '>
          <button type="submit" className='border w-full py-2 px-4 rounded-lg mt-2 bg-cyan-500 text-white font-semibold lg:w-1/6'>Create Supplier</button>
        </div>
      </form>
    </>
  );
};

export default CreateSupplier;
