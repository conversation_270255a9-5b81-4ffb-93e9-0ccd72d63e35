@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Lexend:wght@100..900&family=Manrope:wght@200..800&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;



body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

@layer utilities {
  .for-input {
    @apply border border-gray-300 rounded-md p-2 w-full;
  }
}

.customScroll::-webkit-scrollbar {
  width: 8px;
}

.customScroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.customScroll::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.customScroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}