import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import MainLayout from "./components/MainLayout";
import ResourceManagementPage from "./modules/resources/ResourceManagementPage";
import UserManagementPage from "./modules/users/UserManagementPage";

const App = () => {
  return (
    <Router
      future={{
        v7_startTransition: true,
      }}
    >
      <div className="min-h-screen bg-gray-100">
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route index element={<div className="p-6"><h1 className="text-2xl font-semibold">Dashboard</h1><p>Welcome to PickTrail</p></div>} />
            <Route path="/resources" element={<ResourceManagementPage />} />
            <Route path="/users" element={<UserManagementPage />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
};

export default App;
