import { Box, Modal } from '@mui/material'
import React, { useEffect, useState } from 'react'
import ClearIcon from '@mui/icons-material/Clear';
import { IConsumable } from '../../models/Consumable';
import axios from 'axios';
import { useAuth } from '../../context/AuthContextProvider';
import { transactionService } from '../../services/api';

interface IUpdateModal {
    callOpen: boolean,
    data: IConsumable,
    callClose: () => void
    onAdding: () => void
}

interface IDataToUpdate {
    unitPrice: number,
    quantity: number

}

const UpdateModal = ({ callOpen, data, callClose, onAdding }: IUpdateModal) => {
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const [dataToUpdate, setDataToUpdate] = useState<IDataToUpdate>({
        quantity: 0,
        unitPrice: 0
    })
    const { token } = useAuth()
    const handleClose = () => {
        callClose()
        setOpen(false)
    };
    const handleUpdate = (key: string, value: string) => {
        setDataToUpdate((prev) => {
            return (
                {
                    ...prev,
                    [key]: value
                }
            )
        })


    }
    // ...existing code...
    const handleAddInventory = async () => {

        const inventoryData = {
            consumableSupplierId: data.id,
            actionType: "add", //add pick drop
            unitType: data.unitType,
            quantity: Number(dataToUpdate.quantity),
            staffId: "c51bb67c-bb08-4c91-a9b5-0c085e1d2453",
            pricePerUnit: Number(dataToUpdate.unitPrice),
            projectId: "TestProjectId"
        }

        // consumableId,
        // supplierId,
        // staffId: staff.id,
        // quantity: 50,
        // pricePerUnit: 10,
        // unitType: UnitType.INDIVIDUAL,


        // const response = await axios.post("/api/transactions", inventoryData)
        const response = await transactionService.post("/transactions", inventoryData, token)
        if (response.status === 201) {
            handleClose()
            onAdding()
        }
    }




    useEffect(() => {
        if (callOpen) {
            handleOpen()
        }
    }, [])
    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white outline-none p-5 rounded-md w-[30%] flex flex-col gap-16" >
                <section className='flex flex-col gap-3'>
                    <div className='flex justify-between'>
                        <h1 className='text-xl font-semibold'>Add Inventory</h1>
                        <ClearIcon onClick={handleClose} />
                    </div>
                    <div className='flex gap-2 p-3 bg-slate-200 rounded-xl mt-4 border items-center'>
                        <img src={data.imageUrl} alt="Img" className='w-20 h-20' />
                        <h3 className='text-xl font-semibold' >{data.name}</h3>
                    </div>
                    <div className='flex flex-col gap-1 '>
                        <label htmlFor="" className='text-lg' >Add Qty</label>
                        <input type="text" className='border rounded px-3 py-2' value={dataToUpdate.quantity} onChange={(e) => handleUpdate("quantity", e.target.value)} />
                    </div>
                    <div className='flex flex-col gap-1 '>
                        <label htmlFor="" className='text-lg'>Unit Price</label>
                        <div className='flex gap-3 items-center border rounded px-3'>
                            <p className='text-xl font-semibold'>$</p>
                            <input type="number" className=' px-3 py-2 w-full outline-none' value={dataToUpdate.unitPrice} onChange={(e) => handleUpdate("unitPrice", e.target.value)} />
                        </div>
                    </div>
                </section>
                <section className='flex justify-between gap-4'>
                    <button className='w-full border py-2 font-semibold text-xl rounded-xl border-black' >Cancel</button>
                    <button className='w-full border py-2 font-semibold text-xl rounded-xl bg-blue-500 text-white' onClick={handleAddInventory} >Add</button>
                </section>
            </Box>
        </Modal>
    )
}

export default UpdateModal