import React, { useState, useEffect } from 'react'
import Header from '../../components/general/Header'
import { resourceService } from '../../services/api'
import { useAuth } from '../../context/AuthContextProvider'
import { useNavigate, useParams } from 'react-router-dom'
import toast from 'react-hot-toast'

// Define the Resource interface
interface Resource {
  id?: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  totalAvailableQuantity: number;
  averagePricePerUnit: number;
  minInventoryQty: number;
  maxInventoryQty: number;
  resourceType: string;
}

const AddResource = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState('');
  
  // Resource form state
  const [resource, setResource] = useState<Resource>({
    name: '',
    resourceCode: '',
    unitType: 'individual',
    unitSize: 1,
    imageUrl: '',
    resourceCategory: 'General',
    totalAvailableQuantity: 0,
    averagePricePerUnit: 0,
    minInventoryQty: 0,
    maxInventoryQty: 0,
    resourceType: 'CONSUMABLE'
  });

  // Fetch resource if editing
  useEffect(() => {
    if (isEditing && id) {
      setLoading(true);
      resourceService.get(`/resources/${id}`, token)
        .then(response => {
          setResource(response.data);
          setImagePreviewUrl(response.data.imageUrl);
          setLoading(false);        })
        .catch(err => {
          console.error("Error fetching resource:", err);
          toast.error("Failed to load resource details");
          setLoading(false);
        });
    }
  }, [id, isEditing, token]);
  
  // Update image preview when imageUrl changes
  useEffect(() => {
    if (resource.imageUrl) {
      setImagePreviewUrl(resource.imageUrl);
      setImageError(false);
    } else {
      setImagePreviewUrl('');
    }
  }, [resource.imageUrl]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle numeric inputs
    if (['unitSize', 'totalAvailableQuantity', 'averagePricePerUnit', 'minInventoryQty', 'maxInventoryQty'].includes(name)) {
      setResource({
        ...resource,
        [name]: value === '' ? '' : Number(value)
      });
    } else {
      setResource({
        ...resource,
        [name]: value
      });
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };  
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // First, display an immediate test notification
      toast.success('Form submitted!');
      
      const toastId = toast.loading(isEditing ? 'Updating resource...' : 'Adding resource...');
      
      if (isEditing) {
        await resourceService.put(`/resources/${id}`, resource, token);
        toast.success('Resource updated successfully!', { id: toastId });
      } else {
        await resourceService.post('/resources', resource, token);
        toast.success('Resource added successfully!', { id: toastId });
        // Reset form after successful creation
        setResource({
          name: '',
          resourceCode: '',
          unitType: 'individual',
          unitSize: 1,
          imageUrl: '',
          resourceCategory: 'General',
          totalAvailableQuantity: 0,
          averagePricePerUnit: 0,
          minInventoryQty: 0,
          maxInventoryQty: 0,
          resourceType: 'CONSUMABLE'
        });
        setImagePreviewUrl('');
      }
    } catch (err: any) {
      console.error("Error saving resource:", err);
      const errorMessage = err.response?.data?.message || 'Failed to save resource';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center mb-6">          <button 
            onClick={() => navigate('/resources')}
            className="mr-4 text-blue-600 hover:text-blue-800"
          >
            &larr; Back to Resources
          </button>
          <h1 className="text-2xl font-semibold text-gray-800">
            {isEditing ? 'Edit Resource' : 'Add New Resource'}
          </h1>        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <h2 className="text-lg font-medium mb-4">Basic Information</h2>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resource Name*
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={resource.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter resource name"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resource Code*
                  </label>
                  <input
                    type="text"
                    name="resourceCode"
                    value={resource.resourceCode}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter resource code"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select
                    name="resourceCategory"
                    value={resource.resourceCategory}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="General">General</option>
                    <option value="Electronics">Electronics</option>
                    <option value="Furniture">Furniture</option>
                    <option value="Office Supplies">Office Supplies</option>
                    <option value="Tools">Tools</option>
                    <option value="Equipment">Equipment</option>
                  </select>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resource Type
                  </label>
                  <select
                    name="resourceType"
                    value={resource.resourceType}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="CONSUMABLE">Consumable</option>
                    <option value="ASSET">Asset</option>
                  </select>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image URL
                  </label>
                  <input
                    type="url"
                    name="imageUrl"
                    value={resource.imageUrl}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter image URL"
                  />
                </div>
                
                {/* Image Preview */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image Preview
                  </label>
                  <div className="border border-gray-300 rounded-md h-48 w-full flex items-center justify-center overflow-hidden bg-gray-100">
                    {imagePreviewUrl && !imageError ? (
                      <img
                        src={imagePreviewUrl}
                        alt="Resource preview"
                        className="max-h-full max-w-full object-contain"
                        onError={handleImageError}
                      />
                    ) : (
                      <div className="text-center p-4">
                        {imageError ? (
                          <div className="text-red-500">
                            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <p className="mt-1">Failed to load image</p>
                          </div>
                        ) : (
                          <div className="text-gray-500">
                            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p className="mt-1">No image to preview</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  {resource.imageUrl && imageError && (
                    <p className="text-xs text-red-500 mt-1">
                      The provided URL cannot be displayed. Please check if it's a valid image URL.
                    </p>
                  )}
                </div>
              </div>

              {/* Unit & Inventory Information */}
              <div>
                <h2 className="text-lg font-medium mb-4">Unit & Inventory Information</h2>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unit Type
                    </label>
                    <select
                      name="unitType"
                      value={resource.unitType}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="individual">Individual</option>
                      <option value="pack">Pack</option>
                      <option value="box">Box</option>
                      <option value="case">Case</option>
                      <option value="pallet">Pallet</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unit Size
                    </label>
                    <input
                      type="number"
                      name="unitSize"
                      value={resource.unitSize}
                      onChange={handleInputChange}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Available Quantity
                  </label>
                  <input
                    type="number"
                    name="totalAvailableQuantity"
                    value={resource.totalAvailableQuantity}
                    onChange={handleInputChange}
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price per Unit ($)
                  </label>
                  <input
                    type="number"
                    name="averagePricePerUnit"
                    value={resource.averagePricePerUnit}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Min Inventory
                    </label>
                    <input
                      type="number"
                      name="minInventoryQty"
                      value={resource.minInventoryQty}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Max Inventory
                    </label>
                    <input
                      type="number"
                      name="maxInventoryQty"
                      value={resource.maxInventoryQty}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>            {/* Form Actions */}
            <div className="mt-8 flex justify-end">
              <button
                type="button"
                onClick={() => toast.success('Test notification!')}
                className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none"
              >
                Test Toast
              </button>              <button
                type="button"
                onClick={() => navigate('/resources')}
                className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none disabled:bg-blue-400"
              >
                {loading ? 'Saving...' : isEditing ? 'Update Resource' : 'Add Resource'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddResource;