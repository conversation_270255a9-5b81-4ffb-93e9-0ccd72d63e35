import React, { useRef, useState, useCallback, useEffect } from 'react';
import { IFileUploader, CropState, CroppedArea, MediaSize } from './types';
import { CROP_CONFIG, FILE_CONFIG } from './constants';
import { customSliderStyles } from './styles';
import CropperView from './components/CropperView';
import ImagePreview from './components/ImagePreview';
import UploadPrompt from './components/UploadPrompt';

const FileUploader: React.FC<IFileUploader> = ({ 
    selectedImage, 
    shareFile, 
    isEditable, 
    onImageUpload 
}) => {
    const hiddenFileInput = useRef<HTMLInputElement>(null);
    const [imageToCrop, setImageToCrop] = useState("");
    const [crop, setCrop] = useState<CropState>({ x: 0, y: 0 });
    const [zoom, setZoom] = useState<number>(CROP_CONFIG.ZOOM.default);
    const [cropped, setCropped] = useState<CroppedArea>({
        x: 0,
        y: 0,
        width: 0,
        height: 0,
    });

    // Cleanup URLs on unmount or when imageToCrop changes
    useEffect(() => {
        return () => {
            if (imageToCrop) {
                URL.revokeObjectURL(imageToCrop);
            }
        };
    }, [imageToCrop]);

    const handleClick = useCallback(() => {
        if (!imageToCrop) {
            hiddenFileInput.current?.click();
        }
    }, [imageToCrop]);

    const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>): void => {
        try {
            const file = event.target.files?.[0];
            if (!file) return;
            
            // Validate file size
            if (file.size > FILE_CONFIG.MAX_SIZE) {
                alert('File size too large. Maximum size is 10MB.');
                return;
            }
            
            // Clean up previous URL
            if (imageToCrop) {
                URL.revokeObjectURL(imageToCrop);
            }
            
            const previewUrl = URL.createObjectURL(file);
            setImageToCrop(previewUrl);
        } catch (error) {
            console.error('Error handling file:', error);
            alert('Error loading file. Please try again.');
        }
    }, [imageToCrop]);

    const handleCrop = useCallback(() => {
        if (!cropped || !imageToCrop) return;

        const canvas = document.createElement("canvas");
        canvas.width = cropped.width;
        canvas.height = cropped.height;
        const ctx = canvas.getContext("2d");

        const image = new Image();
        image.src = imageToCrop;

        image.onload = () => {
            if (ctx) {
                ctx.drawImage(
                    image,
                    cropped.x,
                    cropped.y,
                    cropped.width,
                    cropped.height,
                    0,
                    0,
                    cropped.width,
                    cropped.height
                );

                canvas.toBlob(
                    async (blob) => {
                        if (blob) {
                            const croppedFile = new File([blob], FILE_CONFIG.FILENAME, {
                                type: FILE_CONFIG.FORMAT,
                            });
                            const previewUrl = URL.createObjectURL(blob);
                            
                            if (onImageUpload) {
                                onImageUpload(previewUrl);
                            }
                            shareFile(croppedFile);
                            
                            // Clean up
                            URL.revokeObjectURL(imageToCrop);
                            setImageToCrop("");
                        }
                    },
                    FILE_CONFIG.FORMAT,
                    FILE_CONFIG.QUALITY
                );
            }
        };

        image.onerror = () => {
            console.error('Error loading image for cropping');
            alert('Error processing image. Please try again.');
        };
    }, [cropped, imageToCrop, onImageUpload, shareFile]);

    const handleCropClose = useCallback(() => {
        if (imageToCrop) {
            URL.revokeObjectURL(imageToCrop);
        }
        setImageToCrop("");
    }, [imageToCrop]);

    const onCropComplete = useCallback((croppedArea: any, croppedAreaPixels: CroppedArea) => {
        setCropped(croppedAreaPixels);
    }, []);

    const handleOnCropChange = useCallback((cropData: CropState) => {
        setCrop(cropData);
    }, []);

    const handleMediaLoaded = useCallback((mediaSize: MediaSize) => {
        // Adapt zoom based on media size to fit max height
        setZoom(CROP_CONFIG.CONTAINER_HEIGHT / mediaSize.naturalHeight);
    }, []);

    const renderContent = () => {
        if (imageToCrop) {
            return (
                <CropperView
                    imageToCrop={imageToCrop}
                    crop={crop}
                    zoom={zoom}
                    onCropChange={handleOnCropChange}
                    onCropComplete={onCropComplete}
                    onZoomChange={setZoom}
                    onMediaLoaded={handleMediaLoaded}
                />
            );
        }
        
        if (selectedImage) {
            return <ImagePreview selectedImage={selectedImage} />;
        }
        
        return <UploadPrompt />;
    };

    const getContainerClasses = () => {
        const baseClasses = "flex flex-col items-start p-2 w-fit justify-center h-full border-gray-400 bg-white";
        const conditionalClasses = selectedImage || imageToCrop 
            ? "" 
            : "rounded-md border-2 border-dashed border-pickTBlue";
        return `${baseClasses} ${conditionalClasses}`;
    };

    return (
        <>
            <style>{customSliderStyles}</style>
            <div className="flex items-start gap-4">
                <div onClick={handleClick} className={getContainerClasses()}>
                    <div>
                        {renderContent()}
                    </div>
                    <input
                        type="file"
                        ref={hiddenFileInput}
                        accept={FILE_CONFIG.ACCEPT}
                        className="hidden"
                        onChange={handleChange}
                        disabled={!isEditable}
                    />
                </div>
                
                {/* Action buttons - shown only when cropping */}
                {imageToCrop && (
                    <div className="flex flex-col gap-2 mt-2">
                        <button
                            className="border px-4 py-2 rounded bg-white cursor-pointer border-gray-300 hover:bg-gray-50 text-sm font-medium"
                            onClick={handleCropClose}
                        >
                            Cancel
                        </button>
                        <button
                            className="bg-pickTBlue text-white px-4 py-2 rounded-md hover:bg-blue-600 text-sm font-medium"
                            onClick={handleCrop}
                        >
                            Crop & Upload
                        </button>
                    </div>
                )}
            </div>
        </>
    );
};

export default FileUploader;
