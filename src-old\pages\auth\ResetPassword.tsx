import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import InputField from '../../components/ConsumablePage/InputField';

const ResetPassword = () => {
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [errors, setErrors] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();

    // Check if we have the required data in localStorage
    useEffect(() => {
        const token = localStorage.getItem('temp_token');
        const userId = localStorage.getItem('userId');

        if (!token || !userId) {
            // If missing required data, redirect back to login
            navigate('/login');
        }
    }, [navigate]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();        // Validate inputs
        if (!currentPassword) {
            setErrors(['Current password is required']);
            return;
        }

        if (newPassword !== confirmPassword) {
            setErrors(['Passwords do not match']);
            return;
        }

        // Password validation
        if (newPassword.length < 6) {
            setErrors(['Password must be at least 6 characters long']);
            return;
        }

        if (newPassword.length > 30) {
            setErrors(['Password must be no more than 30 characters long']);
            return;
        }

        // Check if password is alphanumeric only
        const alphanumericRegex = /^[a-zA-Z0-9]+$/;
        if (!alphanumericRegex.test(newPassword)) {
            setErrors(['Password must contain only letters and numbers']);
            return;
        }

        setIsLoading(true);
        setErrors([]);

        const userId = localStorage.getItem('userId');
        const token = localStorage.getItem('temp_token');

        try {
            // Configure axios to send the auth token
            const config = {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            };
            // Send request to change password
            await axios.post('/api/auth/change-password', {
                userId,
                currentPassword,
                newPassword,
                confirmPassword
            }, config);            // Clear temporary storage
            localStorage.removeItem('temp_token');
            localStorage.removeItem('userId');

            // Redirect to password reset success page
            navigate('/password-reset-success');
        } catch (error: any) {
            console.error('Password reset error:', error);
            if (error.response && error.response.data) {
                if (error.response.data.message && typeof error.response.data.message === 'object') {
                    // Handle nested message object
                    setErrors([error.response.data.message.message || 'Failed to reset password']);
                } else if (error.response.data.message) {
                    setErrors([error.response.data.message]);
                } else {
                    setErrors(['Failed to reset password. Please try again.']);
                }
            } else {
                setErrors(['Failed to reset password. Please try again.']);
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 font-nunito">
            <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
                <h1 className="text-2xl font-bold mb-6 text-center">Reset Your Password</h1>                
                {/* <p className="mb-4 text-center text-gray-600">
                    You need to set a new password for your account
                </p> */}
                {/* <p className="mb-4 text-center text-gray-600 font-semibold">
                    Please enter your current password, then choose a new password.
                </p>                 */}
                {/* <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
                    <p className="text-sm text-yellow-700">
                        <span className="font-bold">Note:</span> For first-time login, your current password is the temporary password provided to you when your account was created.
                    </p>
                </div> */}
{/* 
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md mb-4">
                    <p className="text-sm text-blue-700 font-semibold mb-1">Password Requirements:</p>
                    <ul className="text-sm text-blue-700 list-disc pl-5">
                        <li>6-30 characters long</li>
                        <li>Letters and numbers only (no special characters)</li>
                    </ul>
                </div> */}                <form onSubmit={handleSubmit} className="space-y-4">
                    <InputField
                        label="Current Password"
                        value={currentPassword}
                        onChange={setCurrentPassword}
                        type="password"
                        error={[]}
                        showPasswordToggle={true}
                    />

                    <InputField
                        label="New Password"
                        value={newPassword}
                        onChange={setNewPassword}
                        type="password"
                        error={[]}
                        showPasswordToggle={true}
                    />

                    <InputField
                        label="Confirm New Password"
                        value={confirmPassword}
                        onChange={setConfirmPassword}
                        type="password"
                        error={errors}
                        showPasswordToggle={true}
                    />{errors.length > 0 && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                            <ul className="list-disc pl-5 text-red-700">
                                {errors.map((error, index) => (
                                    <li key={index}>{error}</li>
                                ))}
                            </ul>
                        </div>
                    )}

                    <button
                        type="submit"
                        className="w-full bg-pickTBlue text-white py-2 rounded-md hover:bg-blue-600 transition-colors disabled:bg-gray-400"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Setting password...' : 'Set New Password'}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default ResetPassword;
