import React from 'react';
import {
    Card,
    CardContent,
    Typography,
    Box,
} from '@mui/material';

interface ProductCategoryCostsProps {
    data: {
        categoryName: string;
        totalCost: number;
    }[];
    formatCurrency: (amount: number) => string;
}

const ProductCategoryCosts: React.FC<ProductCategoryCostsProps> = ({ data, formatCurrency }) => {
    return (
        <Card className="shadow-lg h-full flex flex-col">
            <CardContent className="p-3 flex flex-col h-full">
                <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                        <Typography variant="h6" className="text-white">📦</Typography>
                    </div>
                    <div>
                        <Typography variant="h6" className="font-semibold text-pickTBlue">
                            Product Category Costs
                        </Typography>
                        {/* <Typography variant="body2" color="textSecondary">
                            Spending by category
                        </Typography> */}
                    </div>
                </div>                {data && data.length > 0 ? (
                    <Box className="space-y-2 overflow-y-auto flex-grow" style={{ maxHeight: 'calc(100% - 60px)', overflowY: 'auto' }}>
                        {data.map((category, index) => (
                            <Box key={index} className="bg-secondarybg p-3 rounded-lg border border-gray-200">
                                <Box className="flex justify-between items-center">
                                    <Typography variant="body1" className="font-semibold text-gray-800">
                                        {category.categoryName}
                                    </Typography>
                                    <Typography variant="body1" className="font-bold text-green-600">
                                        {formatCurrency(category.totalCost)}
                                    </Typography>
                                </Box>
                            </Box>
                        ))}
                    </Box>
                ) : (
                    <Box className="text-center py-6 bg-gray-50 rounded-lg border border-gray-200">
                        <Typography variant="body2" color="textSecondary" className="mb-1">
                            📦 No category data available
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                            Product categories will be shown here
                        </Typography>
                    </Box>
                )}
            </CardContent>
        </Card>
    );
};

export default ProductCategoryCosts;
