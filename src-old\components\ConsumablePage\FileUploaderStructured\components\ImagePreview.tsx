import React from 'react';
import { ImagePreviewProps } from '../types';

const ImagePreview: React.FC<ImagePreviewProps> = ({ selectedImage }) => {
    return (
        <div className='relative w-full'>
            <img 
                src={selectedImage} 
                alt="Uploaded" 
                className="w-full max-h-32 rounded-md border-2 border-dashed border-pickTBlue" 
            />
            <button className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black text-white px-3 py-1 rounded-md shadow-md">
                Replace
            </button>
        </div>
    );
};

export default ImagePreview;
