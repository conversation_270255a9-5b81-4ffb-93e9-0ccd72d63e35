import React, { useState } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Collapse,
    Box,
    Typography,
    Chip
} from '@mui/material';
import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';

interface Resource {
    id: string;
    name: string;
    resourceCode: string;
    unitType: string;
    unitSize: number;
    imageUrl: string;
    resourceCategory: string;
    minInventoryQty: number;
    maxInventoryQty: number;
    resourceType: string;
    deletedAt: string | null;
}

interface ResourceCost {
    resource: Resource;
    cost: number;
}

interface Project {
    id: string;
    projectCode: string;
    projectName: string;
    startDate: string;
    dueDate: string;
    createdAt: string;
    updatedAt: string;
}

interface ProjectCostData {
    project: Project;
    totalCost: number;
    resourcesCost: ResourceCost[];
}

interface ProjectCostsTableProps {
    data: ProjectCostData[];
}

const ProjectRow: React.FC<{ projectData: ProjectCostData }> = ({ projectData }) => {
    const [open, setOpen] = useState(false);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <>
            <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
                <TableCell>
                    <IconButton
                        aria-label="expand row"
                        size="small"
                        onClick={() => setOpen(!open)}
                    >
                        {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                    </IconButton>
                </TableCell>
                <TableCell component="th" scope="row">
                    <div>
                        <Typography variant="body1" fontWeight="medium">
                            {projectData.project.projectName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            {projectData.project.projectCode}
                        </Typography>
                    </div>
                </TableCell>
                <TableCell>{formatDate(projectData.project.startDate)}</TableCell>
                <TableCell>{formatDate(projectData.project.dueDate)}</TableCell>
                <TableCell>
                    <Typography variant="body1" fontWeight="bold" color="success.main">
                        {formatCurrency(projectData.totalCost)}
                    </Typography>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
                    <Collapse in={open} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 1 }}>
                            <Typography variant="h6" gutterBottom component="div">
                                Resource Costs
                            </Typography>                            <Table size="small" aria-label="resource costs">
                                <TableHead>
                                    <TableRow>
                                        <TableCell>Resource Name</TableCell>
                                        <TableCell >Cost</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {projectData.resourcesCost.map((resourceCost, index) => (
                                        <TableRow key={index}>
                                            <TableCell component="th" scope="row">
                                                <Typography variant="body2" fontWeight="medium">
                                                    {resourceCost.resource.name}
                                                </Typography>
                                            </TableCell>
                                            <TableCell >
                                                <Typography variant="body2" fontWeight="medium" color="success.main">
                                                    {formatCurrency(resourceCost.cost)}
                                                </Typography>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </Box>
                    </Collapse>
                </TableCell>
            </TableRow>
        </>
    );
};

const ProjectCostsTable: React.FC<ProjectCostsTableProps> = ({ data }) => {
    if (!data || data.length === 0) {
        return (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                    No project cost data available
                </Typography>
            </Paper>
        );
    }

    return (
        <TableContainer component={Paper}>
            <Table aria-label="project costs table">
                <TableHead>
                    <TableRow>
                        <TableCell />
                        <TableCell>Project</TableCell>
                        <TableCell>Start Date</TableCell>
                        <TableCell>Due Date</TableCell>
                        <TableCell>Total Cost</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {data.map((projectData, index) => (
                        <ProjectRow key={projectData.project.id || index} projectData={projectData} />
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
};

export default ProjectCostsTable;
