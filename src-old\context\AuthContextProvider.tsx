import React, { createContext, useState, useContext, useEffect } from "react";
import { JWTPayload } from "../pages/auth/Login";

// interface User {
//   id: string;
//   email: string;
//   name: string;
//   role: string;
// }

interface AuthContextType {
    user: JWTPayload | null;
    loading: boolean;
    token: string | null;
    login: (token: string, userData: JWTPayload) => void;
    logout: () => void;
}

interface AuthProviderProps {
    children: React.ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const AuthContextProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [user, setUser] = useState<JWTPayload | null>(null);
    const [loading, setLoading] = useState(true);
    const [token, setToken] = useState<string | null>(null);    // Load user data from localStorage on initial render
    useEffect(() => {
        const storedToken = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');

        // console.log("storedToken", storedToken)
        // ...existing code...
        
        if (storedToken && storedUser) {
            try {
                const userData = JSON.parse(storedUser) as JWTPayload;
                
                // Check if token is expired
                const isTokenExpired = userData.exp * 1000 < Date.now();
                
                if (isTokenExpired) {
                    // ...existing code...
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    localStorage.removeItem('permissions');
                } else {
                    // ...existing code...
                    setToken(storedToken);
                    setUser(userData);
                }
            } catch (error) {
                console.error('Error parsing stored user data:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('permissions');
            }
        } else {
            // ...existing code...
        }
        
        setLoading(false);
    }, []);

    const login = (token: string, userData: JWTPayload) => {
        setToken(token);
        setUser(userData);
        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(userData));
        
        // Note: Permissions should be set separately when available
    };    const logout = () => {
        setToken(null);
        setUser(null);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('permissions');
    };    const value = {
        user,
        loading,
        token,
        login,
        logout
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export { AuthContext };
export default AuthContextProvider;
