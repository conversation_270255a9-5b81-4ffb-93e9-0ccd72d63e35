import React, { useState, useEffect } from 'react'
import { 
  Box, 
  CircularProgress, 
  Alert, 
  Typography,
  TextField,
  Button,
  Grid2
} from '@mui/material'
import { useAuth } from '../context/AuthContextProvider'
import { consumableService } from '../services/api'
import Header from '../components/general/Header'
import ProjectCostsTable from '../components/ReportPage/ProjectCostsTable'

const Report = () => {
  const [projectCostsData, setProjectCostsData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Date range state
  const getTodayFormatted = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };  const getThreeMonthsAgoFormatted = () => {
    const today = new Date();
    const threeMonthsAgo = new Date(today.setMonth(today.getMonth() - 3));
    return threeMonthsAgo.toISOString().split('T')[0];
  };

  const [startDate, setStartDate] = useState<string>(getThreeMonthsAgoFormatted());
  const [endDate, setEndDate] = useState<string>(getTodayFormatted());
  const [dateError, setDateError] = useState<string | null>(null);
  
  const { token } = useAuth();  const fetchProjectCosts = async (fromDate?: string, toDate?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // ...existing code...
      
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        return;
      }

      // Build query parameters for date range
      const queryParams = new URLSearchParams();
      if (fromDate) queryParams.append('startDate', fromDate);
      if (toDate) queryParams.append('endDate', toDate);
      
      const queryString = queryParams.toString();
      const endpoint = `/reports/project-consumable-costs${queryString ? `?${queryString}` : ''}`;
      
      const response = await consumableService.get(endpoint, token);
      // ...existing code...
      
      if (response.status !== 200) {
        throw new Error(`API returned status ${response.status}`);
      }
      
      if (!response.data) {
        throw new Error('No data received from API');
      }
      
      // The response data should be an array of project cost data
      const projectCosts = Array.isArray(response.data) ? response.data : response.data.projects || [];
      // ...existing code...
      
      setProjectCostsData(projectCosts);
    } catch (error: any) {
      console.error('Detailed error fetching project consumable costs:', error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.message || error.response.statusText;
        setError(`API Error (${status}): ${message}`);
      } else if (error.request) {
        setError('Network error: Unable to reach the server');
      } else {
        setError(`Error: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };  useEffect(() => {
    // Fetch data with default date range (last 3 months to match API default)
    fetchProjectCosts(startDate, endDate);
  }, [token]);

  const handleDateFilter = async () => {
    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end <= start) {
      setDateError('End date must be greater than start date');
      return;
    }

    setDateError(null);
    await fetchProjectCosts(startDate, endDate);
  };  const clearDateFilter = async () => {
    // Reset to default dates (3 months ago to today to match API default)
    const newStartDate = getThreeMonthsAgoFormatted();
    const newEndDate = getTodayFormatted();
    
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setDateError(null);

    // Refetch data with default date range
    await fetchProjectCosts(newStartDate, newEndDate);
  };

  if (loading) {
    return (
      <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
        <Header />
        <Box className="flex-1 flex justify-center items-center">
          <CircularProgress size={60} />
        </Box>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
        <Header />
        <Box className="flex-1 flex justify-center items-center p-6">
          <Alert severity="error" className="max-w-md">
            {error}
          </Alert>
        </Box>
      </div>
    );
  }

  return (
    <div className="bg-mainbg h-screen flex flex-col overflow-hidden">
      <Header />      <Box className="flex-1 overflow-y-auto p-4">
        <Typography variant="h4" className="font-bold text-gray-800 mb-4">
          Reports
        </Typography>        {/* Date Range Filter */}
        <div className="mb-6 p-6 bg-white rounded-lg shadow-sm">
          <h6 className="text-lg font-semibold mb-4 text-gray-800">
            Filter by Date Range
          </h6>
          
          <div className="flex flex-col sm:flex-row sm:items-end sm:justify-end gap-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex flex-col">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  From Date
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => {
                    setStartDate(e.target.value);
                    setDateError(null);
                  }}
                  className={`px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    dateError ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
              </div>
              
              <div className="flex flex-col">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  To Date
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => {
                    setEndDate(e.target.value);
                    setDateError(null);
                  }}
                  className={`px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    dateError ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={handleDateFilter}
                disabled={!startDate || !endDate || loading}
                className="px-6 py-2 bg-blue-500 text-white text-sm font-medium rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                Search
              </button>
              <button
                onClick={clearDateFilter}
                disabled={loading}
                className="px-6 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                Clear
              </button>
            </div>
          </div>
          
          {dateError && (
            <div className="mt-4">
              <p className="text-sm text-red-600 font-medium">
                {dateError}
              </p>
            </div>
          )}
        </div>
        
        <Box>
          <Typography variant="h6" className="mb-4">
            Project Consumable Costs
            {/* <Typography variant="body2" color="text.secondary" component="span" className="ml-2">
              ({new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()})
            </Typography> */}
          </Typography>
          <ProjectCostsTable data={projectCostsData} />
        </Box>
      </Box>
    </div>
  )
}

export default Report
