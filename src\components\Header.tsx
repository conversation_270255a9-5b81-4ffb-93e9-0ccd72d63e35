
import { Bell, ChevronDown } from "lucide-react";
import { Badge } from "./ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

interface HeaderProps {
  title?: string;
}

const Header = ({ title = "Dashboard" }: HeaderProps) => {
  return (
    <header className="flex items-center justify-between py-3 px-10 border-b bg-white">
      <h1 className="text-xl font-semibold text-headingBlack">{title}</h1>
      <div className="flex items-center gap-6">
        <button className="relative p-2 rounded-full hover:bg-gray-100 focus:outline-none">
          <Bell className="w-6 h-6 text-bellBlack" />
        </button>
        <div className="flex items-center gap-2">
          <Badge className="bg-roleBgBlue text-roleTextBlue text-xs font-medium px-3 py-1.5 rounded-full">Admin</Badge>
          <span className="font-semibold text-black"><PERSON></span>
          <Avatar className="w-12 h-12 border-4 border-avatarBorderBlue">
            <AvatarImage src="https://randomuser.me/api/portraits/men/32.jpg" alt="John Doe" />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          <ChevronDown className="w-4 h-4  text-dropArrow ml-1" />
        </div>
      </div>
    </header>
  );
};

export default Header;