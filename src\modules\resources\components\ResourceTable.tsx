import { DataTable } from "@/components/DataTable";
import { columns } from "./columns";
import type { Resource } from "@/types/resource";

interface ResourceTableProps {
  data: Resource[];
  isLoading: boolean;
  error: Error | null;
  onResourceUpdate: () => void;
}

const ResourceTable = ({ data, isLoading, error, onResourceUpdate }: ResourceTableProps) => {
  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error loading resources</div>;
  }

  return (
    <div className="mt-6">
      <DataTable columns={columns({ onResourceUpdate })} data={data || []} />
    </div>
  );
};

export default ResourceTable;
