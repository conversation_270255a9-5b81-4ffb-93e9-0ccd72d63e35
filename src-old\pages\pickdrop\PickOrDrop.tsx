import React from 'react'
import { useNavigate } from 'react-router-dom'

const PickOrDrop = () => {
    const navigate = useNavigate()
    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-mainbg">
            <div className="flex flex-col items-center bg-white p-6 sm:p-10 w-11/12 sm:w-4/5 md:w-2/5 lg:w-1/3 font-lexend rounded-xl shadow-2xl border border-gray-100">
                <h2 className="text-2xl font-bold mb-8 text-gray-800">Select Operation</h2>
                <div className="flex justify-center gap-6 w-full">
                    <button
                        className="bg-pickTBlue hover:bg-blue-600 transition-all duration-300 font-semibold text-white py-3 px-8 rounded-lg shadow-md w-1/2 hover:scale-105 hover:shadow-lg"
                        onClick={() => navigate('/consumables/pick')}
                    >
                        Pick
                    </button>
                    <button
                        className="border-2 border-pickTBlue text-pickTBlue hover:bg-gray-50 transition-all duration-300 font-semibold py-3 px-8 rounded-lg shadow-md w-1/2 hover:scale-105 hover:shadow-lg"
                        onClick={() => navigate('/consumables/drop')}
                    >
                        Drop
                    </button>
                </div>
            </div>
        </div>
    )
}

export default PickOrDrop
