"use client"

import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { Button } from "./ui/button";
import Pagination from "./ui/pagination";
import { usePaginationNumbers } from "@/hooks/use-pagination-numbers";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    columnResizeMode: 'onChange',
  });

  const { pageIndex } = table.getState().pagination;
  const totalPages = table.getPageCount();

  const pageNumbers = usePaginationNumbers({
    currentPage: pageIndex + 1,
    totalPages,
  });

  return (
    <div className="overflow-hidden rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead
                    key={header.id}
                    style={{ width: `${header.getSize()}px` }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Pagination
        currentPage={pageIndex + 1}
        totalPages={totalPages}
        pageNumbers={pageNumbers}
        onPageChange={(page) => table.setPageIndex(page - 1)}
        onPrevious={() => table.previousPage()}
        onNext={() => table.nextPage()}
        canGoPrevious={table.getCanPreviousPage()}
        canGoNext={table.getCanNextPage()}
        totalItems={table.getFilteredRowModel().rows.length}
        startIndex={pageIndex * table.getState().pagination.pageSize}
        endIndex={(pageIndex + 1) * table.getState().pagination.pageSize}
      />
    </div>
  );
}
