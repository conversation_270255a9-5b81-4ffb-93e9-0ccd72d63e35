import React, { useState, useEffect } from 'react';
import CreateSupplier from './CreateSupplier';
import SupplierList from './SupplierList';
import { ISupplier } from '../models/Supplier';

const SupplierManagement: React.FC = () => {
  const [suppliers, setSuppliers] = useState<ISupplier[]>([]);

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/suppliers');
      const data = await response.json();
      setSuppliers(data);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  const handleSupplierCreated = () => {
    fetchSuppliers(); // Refresh the supplier list
  };

  return (
    <div className='p-10  min-h-[100vh]'>
      <h1 className='text-4xl font-bold mb-3 text-start' >Supplier Management</h1>
      <CreateSupplier onSupplierCreated={handleSupplierCreated} />
      <div className='mt-10'>
        <SupplierList suppliers={suppliers} />
      </div>
    </div>
  );
};

export default SupplierManagement;
