import React from 'react'

interface SearchSupplierProps {
    searchTerm: string;
    onSearch: (value: string) => void;
    onCreateNew: () => void;
}

const SearchSupplier: React.FC<SearchSupplierProps> = ({ 
    searchTerm, 
    onSearch,
    onCreateNew 
}) => {
    return (
        <div className=" flex gap-4 items-center justify-between font-nunito">
            <div className="relative ">
                <div className="absolute z-10 inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg className="h-4 w-4 text-[#FE7BA0]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <input
                    type="text"
                    placeholder="Search Consumables..."
                    className="w-full py-2 pl-10 pr-3 rounded-[10px] text-sm font-normal border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => onSearch(e.target.value)}
                />
            </div>
            <button className='text-white text-sm font-medium bg-pickTBlue px-4 py-2 rounded font-lexend' onClick={onCreateNew}>
                New Supllier
            </button>
        </div>
    )
}

export default SearchSupplier 