import PersonIcon from "@mui/icons-material/Person";
import BusinessIcon from "@mui/icons-material/Business";
import SettingsIcon from "@mui/icons-material/Settings";
import { Link, useLocation } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import { useAuth } from "../../context/AuthContextProvider";
import { getUserPermissions } from "../../utils/permissionUtils";

const Header = () => {
  const { user, logout } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [settingsDropdownOpen, setSettingsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const settingsDropdownRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  // Check if current path is pick or drop
  const isPickActive = location.pathname.includes("/consumables/pick");
  const isDropActive = location.pathname.includes("/consumables/drop"); // Get permissions using utility function
  const permissions = getUserPermissions();
  // Check if user has pick and drop permissions
  const hasPickPermission = permissions.includes("pick");
  const hasDropPermission = permissions.includes("drop");
  const hasConfigPermission = permissions.includes("config");

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
      if (
        settingsDropdownRef.current &&
        !settingsDropdownRef.current.contains(event.target as Node)
      ) {
        setSettingsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = () => {
    logout();
    setDropdownOpen(false);
  };

  return (
    <div className="flex justify-between border py-2 px-5 shadow-md h-14">
      <div className="flex gap-3 items-center">
        <Link
          to={user?.roles.includes("admin") ? "/" : "/"}
          className="flex gap-3 items-center"
        >
          <div className="bg-blue-500 p-1 rounded-md">
            <BusinessIcon className=" " />
          </div>{" "}
          <h1 className="text-lg font-semibold">PickTrail</h1>
        </Link>
        {hasPickPermission && (
          <Link to="/consumables/pick">
            <div
              className={`border border-gray-400 rounded-full p-1 overflow-hidden px-5 font-lexend ${
                isPickActive ? "bg-pickTBlue text-white" : ""
              }`}
            >
              Pick
            </div>
          </Link>
        )}
        {hasDropPermission && (
          <Link to="/consumables/drop">
            <div
              className={`border border-gray-400 rounded-full p-1 overflow-hidden px-5 font-lexend ${
                isDropActive ? "bg-pickTBlue text-white" : ""
              }`}
            >
              Drop
            </div>
          </Link>
        )}
      </div>{" "}
      <div className="flex gap-3 items-center">
        {hasConfigPermission && (
          <div className="relative" ref={settingsDropdownRef}>
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => setSettingsDropdownOpen(!settingsDropdownOpen)}
            >
              <SettingsIcon className="text-gray-600" />
            </div>

            {/* Settings dropdown menu */}
            {settingsDropdownOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-50 border">
                <div className="py-1">
                  <Link
                    to="/user-management"
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    User Management
                  </Link>
                  <Link
                    to="/resources"
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    Resource Management
                  </Link>
                  <Link
                    to="/project-management"
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    Project Management
                  </Link>
                  <Link
                    to="/suppliers"
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    Supplier Management
                  </Link>
                  <Link
                    to="/report"
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    Report
                  </Link>
                </div>
              </div>
            )}
          </div>
        )}

        {/* User profile with dropdown */}
        <div className="relative" ref={dropdownRef}>
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => setDropdownOpen(!dropdownOpen)}
          >
            <div className="border rounded-full p-1 overflow-hidden">
              <PersonIcon className="scale-150 translate-y-1" />
            </div>
            <h1 className="text-lg font-semibold">{user?.email || "User"}</h1>
          </div>

          {/* Dropdown menu */}
          {dropdownOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 border">
              <div className="py-1">
                {/* <Link to="/user-management" className='block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100'>
                                    User Management
                                </Link>
                                <Link to="/project-management" className='block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100'>
                                    Project Management
                                </Link>
                                <Link to="/supplier-management" className='block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100'>
                                    Supplier Management
                                </Link> */}
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Header;
