import React from 'react'
import { Edit } from 'lucide-react'

interface Supplier {
    id: string;
    name: string;
    code: string;
    phoneNumber: string;
    contactPerson: string;
    email?: string;
    address?: string;
}

interface SuppliersTableProps {
    suppliers: Supplier[];
    onEdit: (supplier: Supplier) => void;
}

const SuppliersTable: React.FC<SuppliersTableProps> = ({ suppliers, onEdit }) => {
    return (
        <div className=" overflow-x-auto">
            <table className="w-full border-collapse">
                <thead className='font-manrope text-xs font-bold h-9'>
                    <tr className="bg-[#FAFAFA]">
                        <th className="font-medium px-2 text-left border">Supplier Name</th>
                        <th className="font-medium px-2 text-left border">Supplier Code</th>
                        <th className="font-medium px-2 text-left border">Phone Number</th>
                        <th className="font-medium px-2 text-left border">Contact Person</th>
                        <th className="font-medium px-2 text-left border">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {suppliers.map((supplier) => (
                        <tr key={supplier.id} className="hover:bg-gray-50 border-b h-16 text-sm font-semibold font-nunito">
                            <td className="px-4">{supplier.name}</td>
                            <td className="px-4">{supplier.code}</td>
                            <td className="px-4">{supplier.phoneNumber}</td>
                            <td className="px-4">{supplier.contactPerson}</td>
                            <td className="px-4">
                                <button 
                                    onClick={() => onEdit(supplier)}
                                    className="bg-[#707070] hover:bg-gray-600 p-2 rounded-lg"
                                >
                                    <Edit className="text-white h-4 w-4" />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    )
}

export default SuppliersTable 