import type { ResourceType } from "@/types/enums";

export interface ResourceTypeOption {
  value: string;
  label: string;
  description?: string;
}

export const RESOURCE_TYPE_OPTIONS: ResourceTypeOption[] = [
  {
    value: "all",
    label: "All Types",
    description: "Show all resource types"
  },
  {
    value: "consumable",
    label: "Consumable",
    description: "Items that are consumed during use"
  },
  {
    value: "asset",
    label: "Asset",
    description: "Reusable equipment and tools"
  }
];

export const DEFAULT_FILTER_STATE = {
  searchTerm: "",
  selectedType: "all",
  showLowStock: false,
} as const;

export const SEARCH_CONFIG = {
  debounceDelay: 300,
  searchFields: ['name', 'resourceCode'] as (keyof import("@/types/resource").Resource)[],
  placeholder: "Search by item name or code... (Ctrl+K)",
} as const;

export const isValidResourceType = (type: string): type is ResourceType => {
  return type === "consumable" || type === "asset";
};
