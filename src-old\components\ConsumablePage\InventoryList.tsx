import { IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

interface InventoryItem {
    id: string;
    addedOn: string;
    supplier: {
        name: string;
        code: string;
    };
    supplierResourceCode: string;
    pricePerUnit: number;
    availableQuantity: number;
    unitType: string;
    location: string;
}

interface InventoryListProps {
    inventory: InventoryItem[];
    handleDelete: (id: string) => void;
    formatDate: (dateString: string) => string;
}

const InventoryList = ({ inventory, handleDelete, formatDate }: InventoryListProps) => {
    if (!inventory || inventory.length === 0) {
        return (
            <section className="flex flex-col mt-2">
                <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <div className="text-sm">No inventory items found</div>
                    <div className="text-xs mt-1">Add your first item above</div>
                </div>
            </section>
        );
    }

    return (
        <section className="flex flex-col mt-2">
            {inventory.map((data: InventoryItem, index: number) => (
                <div 
                    key={data?.id || index} 
                    className="grid grid-cols-12 py-2 gap-2 items-center px-3 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150"
                >
                    <div className="col-span-2 text-sm font-semibold text-gray-700">
                        {formatDate(data?.addedOn)}
                    </div>
                    <div className="col-span-2 text-sm font-semibold text-gray-900">
                        {data?.supplier?.name}
                    </div>
                    <div className="col-span-1 text-sm font-semibold text-gray-600">
                        {data?.supplier?.code}
                    </div>
                    <div className="col-span-1 text-sm font-semibold text-gray-600">
                        {data?.supplierResourceCode}
                    </div>
                    <div className="col-span-2 text-sm font-semibold text-gray-900">
                        ${data?.pricePerUnit}
                    </div>
                    <div className="col-span-2 text-sm font-semibold text-gray-600">
                        {data?.location}
                    </div>
                    <div className="col-span-1 text-sm font-semibold text-gray-600">
                        {data?.unitType === "pack"
                            ? `${data?.availableQuantity} EA`
                            : data?.availableQuantity}
                    </div>
                    <div className="col-span-1 flex justify-center">
                        <IconButton 
                            aria-label="delete"
                            size="small"
                            onClick={() => handleDelete(data?.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors duration-200"
                        >
                            <DeleteIcon fontSize="small" />
                        </IconButton>
                    </div>
                </div>
            ))}
        </section>
    );
};

export default InventoryList;
