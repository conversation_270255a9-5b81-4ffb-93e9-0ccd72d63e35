import React, { useEffect, useState } from 'react'
import StockTable from '../../components/ConsumablesPage/StockTable'
import { Pagination } from '@mui/material'
import Header from '../../components/general/Header'
import UpdateModal from '../../components/general/UpdateModal'
import axios from 'axios'
import PerPage from '../../components/ConsumablesPage/PerPage'
import { Link } from 'react-router-dom'
import SearchIcon from '@mui/icons-material/Search';
import { IConsumable } from '../../models/Consumable'
import InputField from '../../components/ConsumablePage/InputField'
import { IUser } from '../../models/User'
import { consumableService } from '../../services/api'
import { useAuth } from '../../context/AuthContextProvider'

// Add interface for transactions
interface ITransaction {
  id: string;
  consumableName: string;
  consumableId: string;
  quantity: number;
  unitType: string;
  actionType: string;
  userName: string;
  userId: string;
  timestamp: string;
  projectId?: string;
}

interface ITransactionsResponse {
  transactions: ITransaction[];
}

const Consumables: React.FC = () => {
    const [consumables, setConsumables] = useState<IConsumable[]>([])
    const [filteredConsumables, setFilteredConsumables] = useState<IConsumable[]>([])
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
    const [modalData, setModalData] = useState<IConsumable | null>(null)
    const [search, setSearch] = useState<string>("")

    const [currentPage, setCurrentPage] = useState<number>(1);
    const [itemsPerPage, setItemsPerPage] = useState<number>(25)
    const [totalPages, setTotalPages] = useState<number>(1)
    const [employeeStatus, setEmployeeStatus] = useState(true)
    const { token } = useAuth()
    // const [user, setUser] = useState<IUser>(
    //     {
    //         firstName: "",
    //         lastName: ""
    //     }
    // )

    // Add state for recent activities
    const [recentActivities, setRecentActivities] = useState<ITransaction[]>([]);

    const handleModalOpen: (data: IConsumable) => void = (data) => {
        setIsModalOpen(true)
        setModalData(data)
    }

    const handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void = (e) => {
        const value = e.target.value
        setSearch(value)
        const filtered = consumables.filter((data: any) =>
            data.resourceCode.toLowerCase().includes(value.toLowerCase()) ||
            data.name.toLowerCase().includes(value.toLowerCase())
        ).slice(0, itemsPerPage)
        setFilteredConsumables(filtered);
    }

    const handlePerPageItemChange: (count: number) => void = (count) => {
        if (count > itemsPerPage) {
            const totalPage = Math.ceil(consumables.length / count)
            const filtered = consumables.slice(0, count)
            setItemsPerPage(count)
            setFilteredConsumables(filtered)
            setTotalPages(totalPage)
            setCurrentPage(1)
            return
        }
        const totalPage = Math.ceil(filteredConsumables.length / count)
        const filtered = filteredConsumables.slice(0, count)
        setItemsPerPage(count)
        setFilteredConsumables(filtered)
        setTotalPages(totalPage)
        setCurrentPage(1)
    }

    const handlePageChange = (e: React.ChangeEvent<unknown>, value: number) => {
        const filtered = consumables.slice((value - 1) * itemsPerPage, ((value - 1) * itemsPerPage) + itemsPerPage)
        setFilteredConsumables(filtered)
        setCurrentPage(value)
    }

    const fetchConsumables: () => Promise<void> = async () => {
        // const consumables = await axios.get("/api/consumables/inventory")
        const consumables = await consumableService.get("/resources/inventory", token)
        if (consumables.status === 200) {
            const totalpages = Math.ceil(consumables.data.length / itemsPerPage)
            const filtered = consumables.data.slice(0, itemsPerPage)
            setTotalPages(totalpages)
            setFilteredConsumables(filtered)
            setConsumables(consumables.data)
            // ...existing code...
        }
    }

    // const fetchRecentActivities = async () => {
    //     try {
    //         const response = await consumableService.get("/consumables/inventory/transactions?limit=10&includeZeroQuantity=true", token);
    //         if (response.status === 200) {
    //             setRecentActivities(response.data.transactions);
    //             console.log("Recent activities:", response.data);
    //         }
    //     } catch (error) {
    //         console.error("Error fetching recent activities:", error);
    //     }
    // }

    // const fetchEmploye = async () => {
    //     try {
    //         const employee = await axios.get("/api/users")
    //         console.log("employee")
    //         console.log(employee)
    //         if (employee.status === 200) {
    //             console.log("employee.data")
    //             console.log(employee.data)
    //             if (employee.data.length > 0) {
    //                 localStorage.setItem("employeeid", employee.data[0].id)
    //             } else {
    //                 setEmployeeStatus(false)
    //             }
    //         }
    //     } catch (error) {
    //         console.log(error)
    //     }
    // }

    // const addEmployee = async () => {
    //     const employeeData = {
    //         employeeId: "123456",
    //         firstName: user?.firstName,
    //         lastName: user?.lastName,
    //         role: "admin",
    //         isActive: true
    //     }
    //     const employee = await axios.post("/api/users", employeeData)
    //     if (employee.status === 201) {
    //         localStorage.setItem("employeeid", employee.data.id)
    //         setEmployeeStatus(true)
    //     }
    // }

    useEffect(() => {
        fetchConsumables();
        // fetchRecentActivities();
        // fetchEmploye()
    }, [])

    // ...existing code...

    // Render recent activities section
    const renderRecentActivities = () => {
        return (
            <div className="w-full lg:w-1/4 bg-white rounded-md shadow p-4 h-full overflow-auto">
                <h2 className="text-lg font-medium mb-2">Recent Activities</h2>
                {recentActivities.length > 0 ? (
                    <ul className="divide-y divide-gray-200 customScroll overflow-y-auto max-h-[calc(100%-2rem)]">
                        {recentActivities.map((activity) => (
                            <li key={activity.id} className="py-3">
                                
                                <div className="flex flex-col">
                                    <div className="flex items-center mb-1">
                                        <span className={`mr-2 px-2 py-1 text-xs rounded ${
                                            activity.actionType === 'pick' ? 'bg-red-100 text-red-800' : 
                                            activity.actionType === 'add' ? 'bg-green-100 text-green-800' : 
                                            'bg-blue-100 text-blue-800'
                                        }`}>
                                            {activity.actionType === 'add' ? "ADD" : activity.actionType === 'pick' ? "PICK" : activity.actionType.toUpperCase()}
                                        </span>
                                        <span className="text-sm text-gray-500 ml-auto">
                                            {new Date(activity.timestamp).toLocaleString()}
                                        </span>
                                    </div>
                                    <div>
                                        <span className="font-medium">{activity.userName}</span>
                                        <span className="mx-1">{activity.actionType === 'pick' ? 'picked' : 
                                                              activity.actionType === 'add' ? 'added' : 'dropped'}</span>
                                        <span className="font-medium">{activity.quantity}</span>
                                        <span> </span>
                                        {/* <span className="mx-1">{activity.unitType === 'individual' ? '' : ''}</span> */}
                                        <span className="font-medium">{activity.consumableName}</span>
                                        {/* <span>{activity.}</span> */}
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <p className="text-gray-500">No recent activities</p>
                )}
            </div>
        );
    };

    return (
        <div className='h-screen flex flex-col font-nunito overflow-hidden'>
            <Header />
            {(isModalOpen && modalData) && <div>
                <UpdateModal onAdding={() => fetchConsumables()} data={modalData} callOpen={isModalOpen} callClose={() => setIsModalOpen(false)} />
            </div>}
            <div className='flex flex-col lg:flex-row flex-1 mx-4 my-2 overflow-hidden gap-4'>
                {/* Main content */}
                <div className='flex flex-col justify-between gap-2 rounded-2xl shadow-2xl w-full '>
                    <div className='flex flex-col sm:flex-row justify-between px-3 sm:px-6 py-4 gap-3'>
                        <div className='flex gap-2 items-center border rounded-md bg-white px-3 w-full sm:w-1/4'>
                            <SearchIcon />
                            <input type="text" placeholder='Search Consumables...' value={search} onChange={handleSearch} className='h-full font-normal text-sm rounded-r-md w-full outline-none' />
                        </div>
                        <div className='flex gap-4 font-lexend'>
                            
                            <Link to="/consumables/add">
                                <button className='text-white text-sm font-medium bg-pickTBlue px-4 py-2 rounded'>New Resource</button>
                            </Link>
                        </div>
                    </div>
                    <div className='flex-1 overflow-auto'>
                        <StockTable handleOpen={handleModalOpen} datas={filteredConsumables} />
                    </div>
                    <div className='flex flex-col sm:flex-row items-center justify-between px-3 py-2 bg-[#E7F4FF]'>
                        <Pagination count={totalPages} page={currentPage} variant="outlined" shape="rounded" onChange={handlePageChange} />
                        <PerPage setItemsPerPage={handlePerPageItemChange} />
                    </div>
                </div>
                
                {/* Recent activities - stacks below on mobile, right side on desktop */}
                {/* {renderRecentActivities()} */}
            </div>
        </div>
    )
}

export default Consumables
