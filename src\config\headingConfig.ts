// Configuration for dynamic page headings based on routes
export const headingConfig: Record<string, string> = {
  '/': 'Dashboard',
  '/dashboard': 'Dashboard',
  '/resources': 'Resource Management',
  '/projects': 'Project Management',
  '/inventory': 'Inventory Management',
  '/suppliers': 'Supplier Management',
  '/users': 'User Management',
  '/reports': 'Reports',
  '/pickdrop': 'Pick & Drop',
  '/settings': 'Settings',
};

// Function to get page title from current pathname
export const getPageTitle = (pathname: string): string => {
  // Try exact match first
  if (headingConfig[pathname]) {
    return headingConfig[pathname];
  }
  
  // Try to find the best match for nested routes
  const segments = pathname.split('/').filter(Boolean);
  
  // Check each segment to find a matching base route
  for (let i = segments.length; i > 0; i--) {
    const route = '/' + segments.slice(0, i).join('/');
    if (headingConfig[route]) {
      return headingConfig[route];
    }
  }
  
  // Default fallback
  return 'Dashboard';
};