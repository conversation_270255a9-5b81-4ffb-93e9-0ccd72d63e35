import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON>on } from "@mui/material";
import InputWithSearch from "./InputWithSearch";
import DeleteIcon from "@mui/icons-material/Delete";
import InventoryList from "./InventoryList";
import LocationDropdown from "./LocationDropdown";

export interface ISupplierData {
  id: string;
  name: string;
  code: string;
  unitPrice: number;
  quantity: number;
}

interface IGReceiptInfo {
  addSupplierHandler: (status: boolean) => void;
  handleSupplierData: (key: string, value: string) => void;
  inventory: any;
  setSupplier: (data: any) => void;
  supplier: any;
  supplierData: any;
  handleAddItem: () => void;
  message: { message: string; type: string } | undefined;
  handleDelete: (id: string) => void;
  handleLocationChange?: (location: string, locationName: string) => void;
}

const GReceiptInfo = ({
  supplier,
  setSupplier,
  supplierData,
  inventory,
  handleSupplierData,
  addSupplierHandler,
  handleAddItem,
  message,
  handleDelete,
  handleLocationChange,
}: IGReceiptInfo) => {
  const handleSelection = (supplier: ISupplierData) => {
    // ...existing code...
    setSupplier(supplier);
  };

  // ...existing code...

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    const day = String(date.getUTCDate()).padStart(2, "0"); // Get day and pad with 0 if needed
    const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // Months are 0-based
    const year = date.getUTCFullYear();

    return `${day}-${month}-${year}`;
  }

  function getFormattedDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  }

  return (
    <section className="flex flex-col rounded-lg py-4 px-3 bg-white font-nunito h-full">
      <div className="flex justify-between ">
        <h1 className="text-base font-bold ">Receipt Info</h1>
        {/* {message?.message && <div className='px-4'> */}
        <div className="px-4 ">
          <p
            className={`border h-6 text-xs py-1 px-3 rounded-full w-fit mt-2 transition-opacity duration-300 ease-in-out ${
              message?.message ? "opacity-100" : "opacity-0"
            } ${
              message?.type === "error"
                ? "bg-[#FFE5E5] text-[#FF0000]"
                : "bg-[#DCFFDC] text-[#00BA00]"
            }`}
          >
            {message?.message}
          </p>
        </div>
        <div></div>
        {/* <div className='flex gap-2'>
                    <div className='flex gap-2 items-center'>
                        <Button variant="contained"  >Add</Button>
                    </div>
                </div> */}
      </div>

      <section className="mt-4">
        <div className="flex flex-col ">
          {/* <div className=' items-center border-b text-xs font-bold font-manrope bg-[#FAFAFA] h-9'>
                        <h1 className='px-3 h-full flex items-center col-span-1  ' >Action</h1>
                    </div> */}          <div className="">
            <div className="grid grid-cols-2 gap-4 py-2 px-2">
              {/* Date Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Date</h1>
                <div className="border rounded-md h-11 flex items-center justify-center">
                  <input
                    type="date"
                    className="outline-none w-full px-3"
                    value={supplierData?.addedOn}
                    max={
                      supplierData?.addedOn
                        ? supplierData?.addedOn
                        : getFormattedDate()
                    }
                    onChange={(e) =>
                      handleSupplierData("addedOn", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Supplier Name Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Supplier Name</h1>
                <InputWithSearch
                  selected={handleSelection}
                  addSupplierHandler={addSupplierHandler}
                />
              </div>

              {/* Supplier Code Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Supplier Code</h1>
                <div className="border rounded-md h-11 flex items-center px-3 text-sm font-normal">
                  {supplier?.code}
                </div>
              </div>

              {/* Supplier Resource Code Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Supplier Resource Code</h1>
                <input
                  type="text"
                  value={supplierData?.supplierResourceCode}
                  className="h-11 text-sm font-normal w-full outline-none border rounded-md px-3"
                  onChange={(e) =>
                    handleSupplierData("supplierResourceCode", e.target.value)
                  }
                />
              </div>

              {/* Unit Price Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Unit Price</h1>
                <div className="flex gap-3 border rounded-md h-11 items-center px-3 text-sm font-normal">
                  <p>$</p>
                  <input
                    value={supplierData?.unitPrice}
                    type="number"
                    className="w-full outline-none"
                    onChange={(e) =>
                      handleSupplierData("unitPrice", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Quantity Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Qty</h1>
                <input
                  type="number"
                  value={supplierData?.quantity}
                  className="h-11 w-full outline-none border rounded-md text-sm font-normal px-3"
                  onChange={(e) => handleSupplierData("quantity", e.target.value)}
                />
              </div>

              {/* Location Field */}
              <div className="flex flex-col">
                <h1 className="mb-2 text-sm font-medium">Location</h1>
                <LocationDropdown
                  value={supplierData?.location}
                  onChange={(location) => {
                    handleSupplierData("location", location);
                    if (handleLocationChange) {
                      handleLocationChange(location, location);
                    }
                  }}
                  placeholder="Select location"
                  className="w-full"
                />
              </div>

              {/* Save Button - spans both columns */}
              <div className="col-span-2 flex justify-center mt-4">
                <div
                  className="cursor-pointer rounded-md h-11 flex items-center px-3 justify-center"
                  onClick={handleAddItem}
                >
                  <h3 className="bg-black text-white px-6 py-2 rounded-xl font-manrope text-xs font-bold hover:bg-gray-700 active:bg-gray-900 transition-colors duration-200">
                    Save
                  </h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default GReceiptInfo;
