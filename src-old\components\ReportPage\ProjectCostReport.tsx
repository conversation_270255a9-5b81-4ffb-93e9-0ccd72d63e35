import React from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Fade
} from '@mui/material';

// Update the interface to match the exact structure received from API
interface ProjectCostReportProps {
  reportData: ProjectCost[]; // Using the correct type now
  loading: boolean;
  error: string | null;
}

// Updated to match the exact structure
interface ProjectCost {
  id: string;
  projectCode: string;
  projectName: string;
  totalCost: number;
}

const ProjectCostReport: React.FC<ProjectCostReportProps> = ({
  reportData,
  loading,
  error
}) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <Fade in={!loading}>
      <TableContainer component={Paper}>
        <Table>
          <TableHead className="bg-tableHeaderBg">
            <TableRow>
              <TableCell className="text-xs"><strong>Project Name</strong></TableCell>
              <TableCell className="text-xs"><strong>Total Cost</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {reportData.length > 0 ? (
              reportData.map((project) => (
                <TableRow key={project.id}>
                  <TableCell className="text-sm">
                    {project.projectName} ({project.projectCode})
                  </TableCell>
                  <TableCell className="text-sm">${project.totalCost.toFixed(2)}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={2} align="center" className="text-sm">No data available</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Fade>
  );
};

export default ProjectCostReport;
