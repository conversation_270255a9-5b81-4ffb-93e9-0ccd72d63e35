import React from 'react';
import { ZoomSliderProps } from '../types';
import { CROP_CONFIG } from '../constants';

const ZoomSlider: React.FC<ZoomSliderProps> = ({ zoom, onZoomChange }) => {
    return (
        <div className='w-80 px-2'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
                Zoom: {Math.round(zoom * 100)}%
            </label>
            <input
                type="range"
                min={CROP_CONFIG.ZOOM.min}
                max={CROP_CONFIG.ZOOM.max}
                step={CROP_CONFIG.ZOOM.step}
                value={zoom}
                onChange={(e) => onZoomChange(Number(e.target.value))}
                className='w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider'
            />
        </div>
    );
};

export default ZoomSlider;
