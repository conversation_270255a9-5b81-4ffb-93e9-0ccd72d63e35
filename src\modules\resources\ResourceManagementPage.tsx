import { useState, useCallback, useMemo } from "react";
import ResourceHeader from "./components/ResourceHeader";
import ResourceFilters from "./components/ResourceFilters";
// import ResourceTable from "./components/ResourceTable-v1";
import { LoadingState, ErrorState } from "@/components/common/PageStates";
import { useResources } from "./hooks";
import type { Resource } from "@/types/resource";
import ResourceTable from "./components/ResourceTable";

const ResourceManagementPage = () => {
  const { data: resources, isLoading, error, refetch } = useResources();
  const [filteredResults, setFilteredResults] = useState<Resource[] | null>(null);
  
  // Compute the final filtered resources
  const filteredResources = useMemo(() => {
    if (!resources || !Array.isArray(resources)) return [];
    return filteredResults ?? resources;
  }, [resources, filteredResults]);

  // Handler to receive filtered resources from ResourceFilters component
  const handleFiltered = useCallback((filtered: Resource[]) => {
    const isFiltered = resources && filtered.length !== resources.length;
    setFilteredResults(isFiltered ? filtered : null);
  }, [resources]);
  
  // Clear filters by resetting filtered results
  const clearAllFilters = useCallback(() => {
    setFilteredResults(null);
  }, []);

  // Handle loading state
  if (isLoading) {
    return <LoadingState message="Loading resources..." />;
  }

  // Handle error state
  if (error) {
    return <ErrorState error={error} />;
  }

  // Render main content
  return (
    <div className="p-10 bg-[#FCFCFC]">
      <ResourceHeader onResourceUpdate={refetch} />
      <ResourceFilters 
        resources={resources}
        onFiltered={handleFiltered}
        onClearFilters={clearAllFilters}
      />
      <ResourceTable
        data={filteredResources}
        isLoading={isLoading}
        error={error}
        onResourceUpdate={refetch}
      />
    </div>
  );
};

export default ResourceManagementPage;
