import React, { useState, useContext } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import axios from "axios";
import InputField from "../../components/ConsumablePage/InputField";
import { AuthContext } from "../../context/AuthContextProvider";
import { jwtDecode } from "jwt-decode";

export interface JWTPayload {
  roles: string[];
  sub: string; // subject (usually user id)
  iat: number; // issued at
  exp: number; // expiration time
  email: string;
}

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const auth = useContext(AuthContext);
  const location = useLocation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors([]);

    try {
      const response = await axios.post("/api/auth/login", {
        email,
        password,
      });
      // ...existing code...

      // Safely decode JWT to read claims
      const token = response.data.access_token;
      const decoded: JWTPayload = jwtDecode(token);
      // ...existing code...

      // Check if this is a first-time login (402 Payment Required status)
      if (response.data.isFirstLogin) {
        // Store token temporarily and navigate to password reset page
        localStorage.setItem("temp_token", token);
        localStorage.setItem("userId", decoded.sub);
        navigate("/reset-password");
        return;
      } // Normal login flow
      auth?.login(response.data.access_token, decoded);

      // Get user permissions
      try {
        const permissionsResponse = await axios.get("/api/auth/authorization", {
          headers: { Authorization: `Bearer ${response.data.access_token}` },
        });
        if (permissionsResponse.data && permissionsResponse.data.permissions) {
          localStorage.setItem(
            "permissions",
            JSON.stringify(permissionsResponse.data.permissions)
          );
        }      } catch (permissionError) {
        console.error("Failed to fetch permissions:", permissionError);
      }

      // Redirect all users to dashboard after login
      navigate("/");
    } catch (error: any) {
      if (error.response && error.response.status === 402) {
        // Handle 402 status (First-time login, password change required)
        const token = error.response.data.access_token;
        if (token) {
          const decoded: JWTPayload = jwtDecode(token);
          localStorage.setItem("temp_token", token);
          localStorage.setItem("userId", decoded.sub);

          // Try to get permissions even for first-time login
          try {
            const permissionsResponse = await axios.get(
              "/api/auth/authorization",
              {
                headers: { Authorization: `Bearer ${token}` },
              }
            );
            if (
              permissionsResponse.data &&
              permissionsResponse.data.permissions
            ) {
              localStorage.setItem(
                "permissions",
                JSON.stringify(permissionsResponse.data.permissions)
              );
            }
          } catch (permissionError) {
            console.error(
              "Failed to fetch permissions for new user:",
              permissionError
            );
          }

          navigate("/reset-password");
          return;
        }
      }
      setErrors(["Invalid email or password"]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 font-nunito">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-center">
          Login to PickTrail
        </h1>{" "}
        <form onSubmit={handleSubmit} className="space-y-4">
          <InputField
            label="Email"
            value={email}
            onChange={setEmail}
            type="email"
            error={errors}
          />

          <InputField
            label="Password"
            value={password}
            onChange={setPassword}
            type="password"
            error={errors}
            showPasswordToggle={true}
          />

          <button
            type="submit"
            className="w-full bg-pickTBlue text-white py-2 rounded-md hover:bg-blue-600 transition-colors disabled:bg-gray-400"
            disabled={isLoading}
          >
            {isLoading ? "Logging in..." : "Login"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;
