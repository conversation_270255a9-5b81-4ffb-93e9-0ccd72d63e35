import { Box, Modal, Typography } from '@mui/material'
import React, { useState } from 'react'
import { IoClose } from "react-icons/io5";
import LabeledInput from './LabeledInput';
import axios from 'axios';
import { supplierService } from '../../services/api';
import { useAuth } from '../../context/AuthContextProvider';

interface ISupplierData {
    name: string,
    code: string,
    companyName: string,
    email: string,
    phoneNumber: string,
    address: string,
    contactPerson: string
}

interface IAddSupplierModal {
    handleStatus: (status: boolean) => void
    handleRefetch: () => void
}

const AddSupplierModal = ({ handleStatus, handleRefetch }: IAddSupplierModal) => {
    const [open, setOpen] = useState(true);
    const handleOpen = () => setOpen(true);
    const handleClose = () => {
        handleStatus(false)
        setOpen(false)
    };
    const {token} = useAuth()

    const [supplierData, setSupplierData] = useState<ISupplierData>({
        name: "",
        code: "",
        companyName: "",
        email: "",
        phoneNumber: "",
        address: "",
        contactPerson: ""
    })

    const handleSupplierData = (key: string, value: string) => {
        setSupplierData(prev => {
            return (
                {
                    ...prev,
                    [key]: value
                }
            )
        })
    }

    const handleSave = async () => {
        // const response = await axios.post("/api/suppliers", {
        const response = await supplierService.post("/suppliers", {
            name: supplierData.name,
            code: supplierData.code,
            contactEmail: supplierData.email,
            phoneNumber: supplierData.phoneNumber,
            address: supplierData.address,
            contactPerson: supplierData.contactPerson
        },token)
        console.log(response)
        if (response.status === 201) {
            handleRefetch()
            handleStatus(false)
            setOpen(false)
        }
        console.log(supplierData)
    }

    return (
        <div>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                className=' flex items-center justify-center font-nunito'
            >
                <div className=' w-2/3'>
                    <section className='p-3 border bg-slate-200 rounded-xl '>
                        <div className='flex justify-between'>
                            <h1 className='text-2xl font-bold'>Add Supplier</h1>
                            <div onClick={handleClose}>
                                <IoClose size={32} />
                            </div>
                        </div>
                        <div className='bg-slate-100 p-3 rounded-lg mt-6 grid grid-cols-2 grid-rows-2 gap-5'>
                            <LabeledInput value={supplierData.name} name="name" label='Supplier Name' palceholder='Enter Supplier Name' handleChange={handleSupplierData} />
                            <LabeledInput value={supplierData.code} name="code" label='Supplier Code' palceholder='Enter Code' handleChange={handleSupplierData} />
                            {/* <LabeledInput value={supplierData.companyName} name="companyName" label='Company Name' palceholder='Enter Full Name' handleChange={handleSupplierData} /> */}
                            <LabeledInput value={supplierData.email} name="email" label='Contact Email Id' palceholder='Enter Contact Email Id' handleChange={handleSupplierData} />
                            <LabeledInput value={supplierData.phoneNumber} name="phoneNumber" label='Phone Number' palceholder='Enter Phone Number' type='phoneNumber' handleChange={handleSupplierData} />
                            <LabeledInput value={supplierData.address} name="address" label='Address' palceholder='Enter Address' handleChange={handleSupplierData} />
                            <LabeledInput value={supplierData.contactPerson} name="contactPerson" label='Contact Person' palceholder='Enter Contact Person' handleChange={handleSupplierData} />
                        </div>
                        <div className='mt-10 flex justify-center gap-5'>
                            <button className='w-52 py-1 rounded-lg font-lexend font-medium text-sm border border-black  ' >Cancel</button>
                            <button className='w-52 py-1 rounded-lg font-lexend font-medium text-sm text-white bg-pickTBlue' onClick={handleSave} >Save</button>
                        </div>
                    </section>
                </div>
            </Modal>
        </div>
    )
}

export default AddSupplierModal 