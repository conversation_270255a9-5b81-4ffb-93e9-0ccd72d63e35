export const cropperStyles = {
    containerStyle: {
        background: 'white',
        width: '100%',
        height: '100%',
    },
    mediaStyle: {
        display: 'block',
    },
    cropAreaStyle: {
        border: '2px solid #3b82f6',
    }
};

export const cropperClasses = {
    containerClassName: 'cropper-container',
    mediaClassName: 'cropper-media'
};

export const customSliderStyles = `
    .cropper-container .reactEasyCrop_Container {
        background: white !important;
    }
    .cropper-container .reactEasyCrop_Container::before {
        background: white !important;
    }
    .cropper-container .reactEasyCrop_Container::after {
        background: white !important;
    }
    .cropper-container {
        background: white !important;
    }
    
    /* Custom slider styles */
    .slider::-webkit-slider-thumb {
        appearance: none;
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .slider::-moz-range-thumb {
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .slider::-webkit-slider-track {
        background: #e5e7eb;
        border-radius: 5px;
    }
    
    .slider::-moz-range-track {
        background: #e5e7eb;
        border-radius: 5px;
    }
`;
